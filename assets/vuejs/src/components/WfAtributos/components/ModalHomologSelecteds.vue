<template>
  <div
    class="modal fade"
    id="ModalHomologSelecteds"
    tabindex="-1"
    aria-hidden="true"
  >
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content modal-content-custom">
        <div class="header">
          <h4 v-if="listItensLength > 0" class="modal-title text-title" id="exampleModalLabel">
            Homologar atributos - {{ listItensLength  }} itens selecionados  
          </h4>
          <h4 v-if="listItensLength == 0" class="modal-title text-title" id="exampleModalLabel">
            Não é possível homologar.
          </h4>
          <button
            type="button"
            class="close"
            @click="closeModal"
            aria-label="Close"
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body body" v-show="listItensLength > 0">
          <div class="list">
            <table class="table-movement">
              <thead class="table-movement-thead">
                <tr>
                  <th style="width: 20%">Part number</th>
                  <th>Descrição</th>
                </tr>
              </thead>
              <tbody class="table-movement-tbody">
                <tr
                  v-for="(item, rowIndex) in listItens"
                  :key="item.part_number"
                  :class="{ 'odd-item': rowIndex % 2 === 0 }"
                >
                  <td style="width: 20%">
                    <strong>
                      {{ item.part_number }}
                    </strong>
                  </td>
                  <td>{{ item.descricao }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="form-modal">
            <div>
              <label for="aprove"> Resposta da homologação </label>
              <div class="radio-container">
                <div class="form-check form-check-inline">
                  <input
                    v-model="formValues.homolog_response"
                    class="form-check-input custom-radio"
                    type="radio"
                    name="homolog_response"
                    id="aprove"
                    :value="1"
                    :checked="formValues.homolog_response == 1"
                  />
                  <label
                    class="form-check-label radio-label-fw-normal"
                    for="aprove"
                  >
                    Aprovar
                  </label>
                </div>
                <div class="form-check form-check-inline">
                  <input
                    v-model="formValues.homolog_response"
                    class="form-check-input custom-radio"
                    type="radio"
                    name="homolog_response"
                    id="disapprove"
                    :value="2"
                    :checked="formValues.homolog_response == 2"
                  />
                  <label
                    class="form-check-label radio-label-fw-normal"
                    for="disapprove"
                    >Reprovar</label
                  >
                </div>
              </div>
            </div>
            <div v-if="formValues.homolog_response == 2" class="form-input">
              <label for="justification">Justificativa *</label>
              <textarea
                v-model="formValues.justification"
                name="attr.codigo"
                class="form-control"
                @blur="validateField('justification')"
              ></textarea>
              <span v-if="errors.justification" class="error">
                {{ errors.justification }}
              </span>
            </div>
          </div>
        </div>
        <div class="footer">
          <div>
            <button type="button" class="btn btn-default" @click="closeModal">
              Cancelar
            </button>
            <button v-if="listItensLength > 0" type="button" class="btn btn-success" @click="submit()">
              <i class="glyphicon glyphicon-ok icon"></i> Homologar
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import 'vue-loading-overlay/dist/vue-loading.css';
import Loading from 'vue-loading-overlay';

export default {
  name: 'ModalHomologSelecteds',
  components: {
    Loading,
  },
  props: {
    ncmId: {
      required: true,
      default: Array,
    },
    modalItem: {
      required: true,
      default: Object,
    },
    homologarSemObrigatorios: {
      required: false,
      default: Boolean|Number
    },
    hasObrigatoriosVazio: {
      required: false,
      default: Boolean|Number
    }
  },
  data() {
    return {
      listItens: [], // Inicializa como um array vazio
      status: '',
      formValues: {
        homolog_response: 1,
        justification: '',
      },
      errors: {},
      permite_homologar: false, // Inicializa como false, pois listItens está vazio no início
    };
  },
  watch: {
    listItens: { 
      handler(newValue) {
        this.permite_homologar = newValue.length > 0;  
      },
      deep: true, 
    },
  },
    computed: {
      listItensLength() {
        return this.listItens.length === 1 && this.listItens[0] === "" ? 0 : this.listItens.length;
    },
  },
  mounted() {
    $('.selectpicker').selectpicker();
    $('.selectpicker').selectpicker('refresh');
    //this.listItens = this.modalItem.items;
    this.loadItems();
  },
  methods: {
    getListItensLength() {
      return this.listItens.length === 1 && this.listItens[0] === "" ? 0 : this.listItens.length;
    },
    async loadItems() {
      const arrayIdItem = this.modalItem.items.map(item => item.id_item);
      try {
        const response = await axios.post(
          'atributos/ajax_get_lista_itens_status',
          {
            ncm: this.ncmId,
            idItem: arrayIdItem,
          }
        );
        this.listItens = response.data.data;
        this.$emit('listItensReady', this.listItens); 
      } catch (error) {}
    },
    validateField(field) {
      console.log('validateField', { field, form: this.formValues });
      if (
        field == 'justification' &&
        this.formValues.homolog_response == 2 &&
        this.formValues.justification == ''
      ) {
        this.$set(this.errors, field, 'Este campo é obrigatório.');
      } else if (
        field == 'justification' &&
        this.formValues.homolog_response == 2 &&
        this.formValues.justification != ''
      ) {
        this.$set(this.errors, field, null);
      }
      console.log('validateField error', this.errors);
    },
    async submit() {
        const { homolog_response, justification } = this.formValues;
        if (homolog_response != 1)
        {
 
          this.validateField('justification');
          if (Object.values(this.errors).some((error) => error !== null)) {
            console.log('Corrija os erros antes de enviar o formulário.');
            return;
          }
        }

        const data = {
          homolog_response,
          justification,
          idItem: this.listItens
        };

        if(this.homologarSemObrigatorios == 1 
          && homolog_response == 1 
          && (this.hasObrigatoriosVazio == 1 || this.hasObrigatoriosVazio == true)) 
        {
          swal({
            title: "Atenção",
            type: "warning",
            text: "Este item possui atributos obrigatórios não preenchidos. Deseja homologar mesmo assim?",
            confirmButtonText: "Sim",
            cancelButtonText: "Não",
            showConfirmButton: true,
            showCancelButton: true,
          }).then(async (value) => {
            if (value) {
              const response = await axios.post('atributos/ajax_set_status', data);
              if (response.status === 200 && response.data.err == 0) {
                swal('Sucesso', 'Itens processados: '+ response.data.itens, 'success');
                this.closeModal();
              } else {
                swal('Erros', response.data.msg, 'warning');
              }
            }
          }).catch(() => {
            this.closeModal();
          });
        } else {
          const response = await axios.post('atributos/ajax_set_status', data);
          if (response.status === 200 && response.data.err == 0) {
  
            swal('Sucesso', 'Itens processados: '+ response.data.itens, 'success');
            this.closeModal();
          } else {
            swal('Erros', response.data.msg, 'warning');
          }
          this.closeModal();
        }
    },
    closeModal() {
      this.$emit('closeModal');
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  /* padding: 25px 15px; */
}

.icon {
  font-size: smaller;
  margin-right: 10px;
}

.text-title {
  color: #8d9296;
  font-weight: 600;
}

.text-body {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}

.footer {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: flex-end;
}

.list,
.form-modal {
  display: flex;
  width: 100%;
  margin: 0 0 10px;
}

.form-modal {
  flex-direction: column;
}

.table-movement {
  width: 100%;
  border: 1px solid;
  border-radius: 5px;
}

.table-movement td {
  background: white;
}

.table-movement th,
.table-movement td {
  padding: 10px;
}

.table-movement-thead {
  color: white;
  background-color: #337ab7;
}
.table-movement-tbody {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}

.table-movement-thead,
.table-movement-tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.odd-item td {
  background: #e2e3e5;
}

.move-selected-input {
  width: 100%;
}

.radio-container {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.custom-radio:checked {
  accent-color: #007bff;
}

.form-check-inline {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.form-check-inline input {
  margin: 0;
}

.radio-label-fw-normal {
  font-weight: 400;
}

.form-check-label {
  margin: 0;
}

.form-input {
  position: relative;
}

.error {
  position: absolute;
  bottom: -18px;
  color: red;
  font-size: 0.9em;
}
</style>
