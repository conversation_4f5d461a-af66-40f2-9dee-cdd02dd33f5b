<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

require_once  APPPATH . 'libraries/Spout/Autoloader/autoload.php';

use Box\Spout\Reader\ReaderFactory;
use Box\Spout\Common\Type;

class Uploadnext extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('arquivos_homologacao')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
        $this->load->helper('formatador_helper');
    }

    public function functionShutdown($msg_function)
    {
        $filename = FCPATH . 'assets/logs/log.txt';
        $file = false;
        if (file_exists($filename) && is_writable($filename)) {
            try {
                chmod($filename, 0777);
            } catch (\Throwable $th) {
                return;
            }
            $file = fopen($filename, "a");
        }
        
        if ($file !== false) { // Verifica se o arquivo foi aberto com sucesso
            try {
                fwrite($file, $msg_function . "\n");
            } catch (\Throwable $th) {
                return;
            } finally {
                fclose($file); // Fecha o arquivo
            }
        }
        
    }

    public function index()
    {
        set_time_limit(900);

        $data = array();
        $dados_sistema = '';
        $this->load->model(array(
            'empresa_model',
            'usuario_model',
            'grupo_tarifario_model',
            'grupo_tarifario_excecao_model',
            'cest_model',
            'ex_tarifario_model',
            'cad_item_model',
            'item_model',
            'item_log_model',
            'item_cest_model',
            'ncm_model',
            'suframa_model',
            'cad_item_nve_model',
            'nve_atributo_model',
            'lessin_model',
            'empresa_prioridades_model',
            'cad_item_homologacao_model'
        ));
        $this->load->helper('formatador_helper');

        $data['empresas'] = $this->empresa_model->get_all_entries();

        $empresaAtual = $this->empresa_model->get_entry(sess_user_company());

        $campos_adicionais_empresa = explode('|', $empresaAtual->campos_adicionais);

        $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais_empresa);

        if ($hasDescricaoGlobal) {
            $arquivo_modelo = base_url('assets/excel_modelo/itens-para-homologacao_com_descricao_global.xlsx');
        } else {
            $arquivo_modelo = base_url('assets/excel_modelo/itens-para-homologacao.xlsx');
        }

        $data['arquivo_modelo'] = $arquivo_modelo;

        if ($this->input->post('submit')) {
        $forcar_homologacao = FALSE;
		$inicio = microtime(true);

            $upload_path = config_item('upload_tmp_path');

            $this->load->library('form_validation');

            $this->form_validation->set_rules('id_empresa', 'Empresa', 'trim|required');

            if ($this->form_validation->run() == TRUE) {
                $id_empresa = $this->input->post('id_empresa');
                $validar    = (bool) $this->input->post('validar');

                // resgata a empresa (que contém o segmento para validação posterior)
                $empresa = $this->empresa_model->get_entry($id_empresa);
                
                $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
                $can_formatar_texto = company_can("formatar_texto", $funcoes_adicionais);

                $config['upload_path'] = $upload_path;
                $config['allowed_types'] = 'xlsx';
                $config['max_size'] = 2147483648;

                $this->load->library('unzip');
                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('arquivo')) {
                    $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . $this->upload->display_errors('<li>', '</li>') . "</ul>";
                    $this->message_on_render($err, 'error');
                } else {
                    $log_inseridos = array();
                    $log_atualizados = array();
                    $log_com_erro = array();

                    $upload_data = $this->upload->data();

                    $file_ext = strtolower($upload_data['file_ext']);

                    $id_empresa = $this->input->post('id_empresa');
                    $item_importado_default = $this->empresa_model->check_imported_item_permission($id_empresa);

                    // xlsx file
                    if ($file_ext == '.xlsx') {

                        $msg = "Data: ".date('d/m/Y \à\s H:i:s')." uploadnext - INICIO "." Empresa: ".sess_user_company()." Usuario: ".sess_user_id()." Nome do arquivo: ".$upload_data['file_name']." local: ".$upload_data['full_path'];
                        register_shutdown_function([$this, 'functionShutdown'],$msg);

                        $this->load->helper('text_helper');

                        $vfapm = 0;
                        $novoPeso = "";
                        $novaLista = "";
                        $novaPrioridade = "";
                        $novaMaquina = "";
                        $novaOrigem = "";
                        $novasObservacoes = "";

                        $error_log_lang = $this->lang->load('error_log', 'portuguese', TRUE);

                        $reader = ReaderFactory::create(Type::XLSX);
                        $reader->open($upload_data['full_path']);

                        $colunas = array(
                            'part_number'                   => array('PN', 'Codigo', 'Codigo\s(do\s)?(Item|Produto)', 'Part Number'),
                            'estabelecimento'               => array('Estab(\.)?', 'Estabelecimento', 'ESTABELECIMENTO'),
                            'observacoes'                   => array('observacoes', 'observacões', 'OBSERVACOES', 'OBSERVACÕES'),
                            'descricao_proposta_resumida'   => array('Desc(\.|ricao)?\sProposta\sResumida'),
                            'descricao_proposta_completa'   => array('Desc(\.|ricao)?\sProposta\sCompleta'),
                            'grupo_tarifario'               => array('GRUPO', 'Grupo', 'Grupo\sTarifario'),
                            'caracteristicas'               => array('Caracteristica(s)?'),
                            'subsidio'                      => array('Subsidio'),
                            'id_resp_fiscal'                => array('Fiscal', 'Resp(\.|onsavel)?\sFiscal'),
                            'id_resp_engenharia'            => array('(Engenheiro|Tecnico)', 'Resp(\.|onsavel)?\s(Tecnico|Engenharia|Engenheiro)'),
                            'forcar_atualizacao'            => array('Forcar','Atualizar', 'Forcar\sAtualizacao(\s\(Sim\/Nao\))?', 'Atualizar(\s\(Sim\/Nao\))?', 'ATUALIZAR (\s\(Sim\/Nao\))?'),
                            'memoria_classificacao'         => array('Memoria', 'Memoria\s(de\s)?Classificacao'),
                            'cnpj'                          => array('CNPJ', 'Empresa'),
                            'evento'                        => array('Evento'),
                            'dispositivos_legais'           => array('Dispositivo\sLegal', 'Dispositivos\sLegais'),
                            'solucao_consulta'              => array('Solucao', 'Solucao\s(de\s)?Consulta'),
                            'funcao'                        => array('Funcao'),
                            'peso'                          => array('Peso'),
                            'prioridade'                    => array('prioridade', 'Prioridade', 'PRIORIDADE'),
                            'inf_adicionais'                => array('Informacoes Adicionais', 'Informações Adicionais', 'INFORMAÇÕES ADICIONAIS', 'INFORMACOES ADICIONAIS'),
                            'aplicacao'                     => array('Aplicacao'),
                            'marca'                         => array('Marca'),
                            'material_constitutivo'         => array('Material', 'Material\sConstitutivo'),
                            'cod_cest'                      => array('CEST', 'Codigo\sCEST', 'CEST\sProposto'),
                            'status_simplus'                => array('(.*?)Simplus(.*?)'),
                            'li'                            => array('LICENCIAMENTO NÃO AUTOMATICO', 'licenciamento nao automatico'),
                            'li_orgao_anuente'              => array('ORGÃO ANUENTE', 'ORGAO ANUENTE'),
                            'li_destaque'                   => array('DESTAQUE LI'),
                            'antidumping'                   => array('ANTIDUMPING ATIVO'),
                            'suframa_descricao'             => array('DESCRIÇÃO SUFRAMA', 'DESCRICAO SUFRAMA'),
                            'suframa_codigo'                => array('CÓDIGO PRODUTO SUFRAMA', 'CODIGO PRODUTO SUFRAMA'),
                            'ex_ii'                         => array('EX-II'),
                            'texto_ex_ii'                   => array('TEXTO DO EX-II'),
                            'ex_ipi'                        => array('EX-IPI'),
                            'texto_ex_ipi'                  => array('TEXTO DO EX-IPI'),
                            'nve_atributo_aa'               => array('NVE ATRIBUTO AA'),
                            'nve_valor_aa'                  => array('NVE VALOR AA'),
                            'nve_atributo_ab'               => array('NVE ATRIBUTO AB'),
                            'nve_valor_ab'                  => array('NVE VALOR AB'),
                            'nve_atributo_ac'               => array('NVE ATRIBUTO AC'),
                            'nve_valor_ac'                  => array('NVE VALOR AC'),
                            'nve_atributo_ad'               => array('NVE ATRIBUTO AD'),
                            'nve_valor_ad'                  => array('NVE VALOR AD'),
                            'nve_atributo_ae'               => array('NVE ATRIBUTO AE'),
                            'nve_valor_ae'                  => array('NVE VALOR AE'),
                            'nve_atributo_af'               => array('NVE ATRIBUTO AF'),
                            'nve_valor_af'                  => array('NVE VALOR AF'),
                            'nve_atributo_ag'               => array('NVE ATRIBUTO AG'),
                            'nve_valor_ag'                  => array('NVE VALOR AG'),
                            'nve_atributo_ah'               => array('NVE ATRIBUTO AH'),
                            'nve_valor_ah'                  => array('NVE VALOR AH'),
                            'nve_atributo_u'                => array('NVE ATRIBUTO U'),
                            'nve_valor_u'                   => array('NVE VALOR U'),
                            'nve_atributo_ue'               => array('NVE ATRIBUTO UE'),
                            'nve_valor_ue'                  => array('NVE VALOR UE'),
                            'lista_cliente'                 => array('FAZ PARTE DA LISTA CLIENTE'),
                            'maquina'                       => array('maquina', 'Maquina', 'MAQUINA'),
                            'origem'                        => array('origem', 'Origem', 'ORIGEM', 'ORIGEM (PAIS)'),
                            'cod_owner'                     => array('OWNER', 'CODIGO DO OWNER', 'Owner'),
                            'descricao_global'              => array('DESCRICAO_GLOBAL', 'Descrição Global', 'Desc(\.|ricao)?\sGlobal'),
                            'forcar_homologacao'            => array('FORCAR HOMOLOGACAO', 'FORÇAR'),
                            'gestao_mensal'                 => array('GESTAO_MENSAL', 'GESTAO MENSAL', 'gestao_mensal'),
                            'importado'                     => array('IMPORTADO'),
                        );

                        $validar_erros = 0;
                        $count_register = 0;
                        foreach ($reader->getSheetIterator() as $sheet) {
                            if ($sheet->getIndex() === 0) {
                                foreach ($sheet->getRowIterator() as $i => $row) {
                                    $count_register++;
                                    if ($i == 1) {
                                        $idx = array();

                                        $cols = array_map(function ($item) {
                                            return convert_accented_characters($item);
                                        }, array_values($row));

                                        foreach ($cols as $k => $row_col) {
                                            foreach ($colunas as $ck => $regras) {
                                                $preg = '/^(' . implode("|", $regras) . ')$/i';

                                                if (preg_match($preg, $row_col)) {
                                                    if (array_key_exists($ck, $idx)) continue 2;

                                                    unset($cols[$k]);
                                                    $idx[$ck] = $k;

                                                    continue 2;
                                                }
                                            }
                                        }
                                        // Colunas inválidas/duplicadas
                                        $erro_proc_col = FALSE;
                                        $msgs_err = '';

                                        if (count($cols) > 0) {
                                            $lista_colunas = '';

                                            foreach ($cols as $kv => $colv) {
                                                $alpha = num2alpha($kv);
                                                $col_info = (empty($row[$kv]) ? '<em>Sem título</em>' : $row[$kv]);
                                                $lista_colunas .= '<li>' . $col_info . ' (<b>' . $alpha . '</b>)</li>';
                                            }

                                            $msg_err  = "<li>Colunas inválidas ou duplicadas: <b>" . count($cols) . "</b></li>";
                                            $msg_err .= "<ul>" . $lista_colunas . "</ul>";

                                            $erro_proc_col = TRUE;
                                        }

                                        // Campos Obrigatórios
                                        if (!isset($idx['part_number'])) {
                                            $msg_err .= '<li>Coluna <b>Part Number</b> não encontrada.</li>';
                                            $erro_proc_col = TRUE;
                                        }

                                        if ($erro_proc_col === TRUE) {
                                            $message = '<h4>Ops.. Alguns erros foram encontrados na planilha enviada:</h4><ul>' . $msg_err . '</ul>';
                                            $this->message_next_render($message, 'error');

                                            redirect('uploadnext');
                                        }
                                    } else {
                                        $empty = true;
                                        for ($i = 0; $i <= count($row) -1; $i++) {
                                            if (!empty($row[$i]))
                                            {
                                                $empty = false;
                                            }
                                        }

                                        if ($empty == true)
                                            continue;  

                                        // Limpeza de Caracteres Especiais
                                        array_walk($row, function (&$v) {
                                            $v = clean_str($v);
                                        });

                                        if (isset($idx['part_number'])) {
                                            $part_number = clean_str($row[$idx['part_number']], true);
                                        }

                                        if (isset($idx['estabelecimento'])) {
                                            $estabelecimento = $row[$idx['estabelecimento']];
                                            $estabelecimento = preg_replace('/[\xA0]/u', '', trim($estabelecimento));

                                            // Caso não tenha nenhum Estabelecimento
                                            if (empty($estabelecimento)) {
                                                $estabelecimento = $empresa->estabelecimento_default;
                                            }
                                        } else {
                                            $estabelecimento = $empresa->estabelecimento_default;
                                        }

                                        $estabelecimento = convert_accented_characters($estabelecimento);

                                        // Validação PN
                                        if (empty($part_number)) {
                                             $log_com_erro['part_number_vazio'][] = array('linha' => $i);
                                            continue;
                                        }

                                        // Validação Estabelecimento
                                        if (empty($estabelecimento)) {
                                            $log_com_erro['estabelecimento_vazio'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                            continue;
                                        }
                                        $dbdata = [];
                                        $id_resp_fiscal = $id_resp_engenharia = null;
                                        
                                        if (isset($idx['id_resp_fiscal'])) {
                                            /* Responsável Fiscal */
                                            $email_resp_fiscal = trim($row[$idx['id_resp_fiscal']]);

                                            if (!empty($email_resp_fiscal)) {
                                                $id_resp_fiscal = $this->usuario_model->get_user_id_by_email($email_resp_fiscal);

                                                if (empty($id_resp_fiscal)) {
                                                    $log_com_erro['usuario_fiscal_nao_encontrado'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                    $validar_erros++;
                                                } else {
                                                    $dbdata['id_resp_fiscal'] = $id_resp_fiscal;
                                                }
                                            } else {
                                                $log_com_erro['usuario_fiscal_vazio'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                $validar_erros++;
                                            }
                                        }

                                        if (isset($idx['id_resp_engenharia'])) {
                                            /* Responsável Engenharia */
                                            $email_resp_engenharia = trim($row[$idx['id_resp_engenharia']]);

                                            if (!empty($email_resp_engenharia)) {
                                                $id_resp_engenharia = $this->usuario_model->get_user_id_by_email($email_resp_engenharia);

                                                if (empty($id_resp_engenharia)) {
                                                    $log_com_erro['usuario_engenheiro_nao_encontrado'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                    $validar_erros++;
                                                } else {
                                                    $dbdata['id_resp_engenharia'] = $id_resp_engenharia;
                                                }
                                            } else {
                                                $log_com_erro['usuario_engenheiro_vazio'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                $validar_erros++;
                                            }
                                        }

                                        $usuario_log_adicional = '';
                                        $usuario_total_com_erro = 0;
                                        $novo_grupo_tarifario = '';
                                         try {
                                            // check item
                                            $this->db->where('part_number', $part_number);
                                            $this->db->where('id_empresa', $id_empresa);
                                            $this->db->where('estabelecimento', $estabelecimento);

                                            $item = $this->db->get('item');

                                            if ($item->num_rows() == 0) {
                                                $log_com_erro['part_number_inexistente'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                continue;
                                            }

                                            // Informações da tabela ITEM.
                                            $item_base = $item->row();

                                            $dbdata['id_empresa'] = $id_empresa;
                                            $dbdata['part_number'] = $part_number;
                                            $dbdata['estabelecimento'] = $estabelecimento;

                                            if (isset($idx['grupo_tarifario'])) {
                                                $grupo_tarifario_desc = html_entity_decode(trim($row[$idx['grupo_tarifario']]));

                                                try {
                                                    if (in_array('exibir_id_gpt', $funcoes_adicionais)) {
                                                        $gt = $this->grupo_tarifario_model->get_entry($row[$idx['grupo_tarifario']]);

                                                    } else {
                                                        $gt = $this->grupo_tarifario_model->get_entry_by_desc($grupo_tarifario_desc);
  
                                                    }
                                                } catch (Exception $e) {
                                                    $log_com_erro['grupo_tarifario_desconhecido'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                    continue;
                                                }

                                                if ($gt->ativo == 0) {
                                                    $log_com_erro['grupo_tarifario_inativo'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                    continue;
                                                }

                                                $dbdata['id_grupo_tarifario'] = $id_grupo_tarifario = $gt->id_grupo_tarifario;
                                                $dbdata['ncm_proposto']       = $gt->ncm_recomendada;

                                            }

                                            //ANTIDUMPING
                                            if (isset($idx['antidumping']) && !empty($row[$idx['antidumping']])) {
                                                $dbdata['antidumping'] = $row[$idx['antidumping']];
                                            }

                                            /* DESCRIÇÃO PROPOSTA RESUMIDA:
                                             * Se o grupo for automático e o campo for nulo,
                                             * Salva a descrição na tabela com base no que está definido no cadastro do grupo tarifário.
                                             */
                                            if (isset($idx['descricao_proposta_resumida'])) {
                                         
                                                $descricao_proposta_resumida = formatar_texto($can_formatar_texto, trim($row[$idx['descricao_proposta_resumida']]));

                                                if (empty($descricao_proposta_resumida)) {
                                                    if (isset($gt)) {
                                                        // Tipo de atualização for automática (diferente de Manual)
                                                        if ($gt->tipo_descricao_resumida == NULL) {
                                                            $log_com_erro['descricao_erro'][] = array($part_number, $estabecimento);
                                                            $validar_erros++;
                                                        } else if ($gt->tipo_descricao_resumida_id == 1) {
                                                            $log_com_erro['descricao_erro_grupo_manual'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                            $validar_erros++;
                                                        } else if ($gt->tipo_descricao_resumida_id == 4 && empty($gt->descricao_resumida)) {
                                                            $log_com_erro['grupo_descricao_automatica_vazia'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                            $validar_erros++;
                                                        } else {
                                                            $dbdata['descricao_mercado_local'] = formatar_texto($can_formatar_texto, $this->grupo_tarifario_model->create_descricao_resumida($part_number, $id_empresa, $gt->id_grupo_tarifario));
                                                            $dbdata['forma_descricao_sugerida'] = $gt->tipo_descricao_resumida_id;
                                                        }
                                                    } else {
                                                        $dbdata['descricao_mercado_local'] = formatar_texto($can_formatar_texto, $descricao_proposta_resumida);
                                                    }
                                                } else {
                                                    $max_chars = $empresa->descricao_max_caracteres;

                                                    if (isset($row['descricao_proposta_resumida']) && !empty($max_chars) && strlen($row['descricao_proposta_resumida']) > $max_chars) {
                                                        $log_com_erro['descricao_maximo_de_caracteres'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                        $validar_erros++;
                                                    } else {
                                                        $dbdata['descricao_mercado_local'] = formatar_texto($can_formatar_texto, $descricao_proposta_resumida);
                                                    }
                                                }
                                            }

                                            /* CARACTERÍSTICA:
                                             * Se o campo da planilha for nulo, verifica se existe uma exceção e a utiliza, se não preenche com a informação do grupo tarifário.
                                             * Se no grupo tarifário for nulo, ocorre o erro.
                                             */
                                            if (isset($idx['caracteristicas'])) {
                                                $caracteristicas = trim($row[$idx['caracteristicas']]);

                                                if (empty($caracteristicas)) {
                                                    if (isset($gt)) {
                                                        if ($excecao = $this->grupo_tarifario_excecao_model->get_excecao_empresa('caracteristica', $id_empresa, $gt->id_grupo_tarifario)) {
                                                            $dbdata['caracteristicas'] = $excecao->descricao;
                                                        } else if (!empty($gt->caracteristica)) {
                                                            $dbdata['caracteristicas'] = $gt->caracteristica;
                                                        } else {
                                                            $log_com_erro['caracteristica_erro'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                            $validar_erros++;
                                                        }
                                                    } else {
                                                        $dbdata['caracteristicas'] = $caracteristicas;
                                                    }
                                                } else {
                                                    $dbdata['caracteristicas'] = $caracteristicas;
                                                }
                                            }

                                            /* SUBSÍDIO:
                                             * Se o campo da planilha for nulo, verifica se existe uma exceção e a utiliza, se não preenche com a informação do grupo tarifário.
                                             * Se no grupo tarifário for nulo, ocorre o erro.
                                             */
                                            if (isset($idx['subsidio'])) {
                                                $subsidio = trim($row[$idx['subsidio']]);

                                                if (empty($subsidio)) {
                                                    if (isset($gt)) {
                                                        if ($excecao = $this->grupo_tarifario_excecao_model->get_excecao_empresa('subsidio', $id_empresa, $gt->id_grupo_tarifario)) {
                                                            $dbdata['subsidio'] = $excecao->descricao;
                                                        } else if (!empty($gt->subsidio)) {
                                                            $dbdata['subsidio'] = $gt->subsidio;
                                                        } else {
                                                            $log_com_erro['subsidio_erro'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                            $validar_erros++;
                                                        }
                                                    } else {
                                                        $dbdata['subsidio'] = $subsidio;
                                                    }
                                                } else {
                                                    $dbdata['subsidio'] = $subsidio;
                                                }
                                            }

                                            /* MEMORIA DE CLASSIFICAÇÃO:
                                             * Se o campo da planilha for nulo, verifica se existe uma exceção e a utiliza, se não preenche com a informação do grupo tarifário.
                                             * Campo opcional
                                             */

                                            if (isset($idx['memoria_classificacao'])) {
                                                $memoria_classificacao = trim($row[$idx['memoria_classificacao']]);
                                                $dbdata['memoria_classificacao'] = $memoria_classificacao;

                                                if (empty($memoria_classificacao) && isset($gt)) {
                                                    if ($excecao = $this->grupo_tarifario_excecao_model->get_excecao_empresa('memoria_classificacao', $id_empresa, $gt->id_grupo_tarifario)) {
                                                        $dbdata['memoria_classificacao'] = $excecao->descricao;
                                                    } else if (!empty($gt->memoria_classificacao)) {
                                                        $dbdata['memoria_classificacao'] = $gt->memoria_classificacao;
                                                    }
                                                }
                                            }

                                            /* DISPOSITIVO LEGAL:
                                             * Se o campo da planilha for nulo, verifica se existe uma exceção e a utiliza, se não preenche com a informação do grupo tarifário.
                                             * Campo opcional
                                             */
                                            if (isset($idx['dispositivo_legal'])) {
                                                $dispositivo_legal = trim($row[$idx['dispositivo_legal']]);
                                                $dbdata['dispositivo_legal'] = $dispositivo_legal;

                                                if (empty($dispositivo_legal) && isset($gt)) {
                                                    if ($excecao = $this->grupo_tarifario_excecao_model->get_excecao_empresa('dispositivo_legal', $id_empresa, $gt->id_grupo_tarifario)) {
                                                        $dbdata['dispositivo_legal'] = $excecao->descricao;
                                                    } else if (!empty($gt->dispositivo_legal)) {
                                                        $dbdata['dispositivo_legal'] = $gt->dispositivo_legal;
                                                    }
                                                }
                                            }

                                            /* SOLUÇÃO DE CONSULTA:
                                             * Se o campo da planilha for nulo, verifica se existe uma exceção e a utiliza, se não preenche com a informação do grupo tarifário.
                                             * Campo opcional
                                             */
                                            if (isset($idx['solucao_consulta'])) {
                                                $solucao_consulta = trim($row[$idx['solucao_consulta']]);
                                                $dbdata['solucao_consulta'] = $solucao_consulta;

                                                if (empty($solucao_consulta) && isset($gt)) {
                                                    if ($excecao = $this->grupo_tarifario_excecao_model->get_excecao_empresa('solucao_consulta', $id_empresa, $gt->id_grupo_tarifario)) {
                                                        $dbdata['solucao_consulta'] = $excecao->descricao;
                                                    } else if (!empty($gt->solucao_consulta)) {
                                                        $dbdata['solucao_consulta'] = $gt->solucao_consulta;
                                                    }
                                                }
                                            }

                                            if (isset($idx['descricao_global'])) {

                                                $this->load->model('item_model');

                                                $descricao_global = trim($row[$idx['descricao_global']]);

                                                $estabelecimentoItem = $row[$idx['estabelecimento']];

                                                $atualizaItem = array(
                                                    'descricao_global' => $descricao_global
                                                );

                                                $this->item_model->update_item($part_number, $id_empresa, $atualizaItem, $motivo=FALSE, $estabelecimentoItem);

                                            }

                                            $campos_adicionais = explode('|', $empresa->campos_adicionais);
                                            $has_campo_adicional = FALSE;

                                            $has_funcao = FALSE;
                                            $has_aplicacao = FALSE;
                                            $has_marca = FALSE;
                                            $has_material_constitutivo = FALSE;
                                            $has_descricao_proposta_completa = FALSE;
                                            $has_inf_adicionais = FALSE;
                                            $has_owner = FALSE;
                                            $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais);

                                            /* FUNÇÃO:
                                             * Campo opcional
                                             */

                                            if (isset($idx['funcao'])) {
                                                $dbdata['funcao'] = formatar_texto($can_formatar_texto, trim($row[$idx['funcao']]));

                                                if (in_array('funcao', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                    $has_funcao = TRUE;
                                                }
                                            }

                                            /* OWNER:
                                             * Campo opcional
                                             */
                                            if (isset($idx['cod_owner']) && !empty($row[$idx['cod_owner']])) {
                                                $this->load->model('owner_model');
                                                $this->load->model('item_model');
                                                $owner = trim($row[$idx['cod_owner']]);
                                                $force = (isset($idx['forcar_atualizacao']) ? mb_strtolower(trim($row[$idx['forcar_atualizacao']])) : 'nao');

                                                $codigo_owner_dbdata = $this->owner_model->get_owner($owner);
                                                if (in_array('owner', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                    $has_owner = TRUE;
                                                }

                                                if ($force == 'sim') {
                                                    if (empty($codigo_owner_dbdata)) {
                                                        // Incluir mensagem de erro
                                                        $log_com_erro['owner_inexistente'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                        $validar_erros++;
                                                    } else {
                                                        $atualizaOwner = array(
                                                            'cod_owner' => $codigo_owner_dbdata->codigo
                                                        );
                                                        $this->item_model->update_item(
                                                            $part_number,
                                                            $id_empresa,
                                                            $atualizaOwner,
                                                            false,
                                                            $estabelecimento
                                                        );
                                                    }
                                                }
                                            }

                                            /* PESO:
                                             * Campo opcional
                                             */
                                            if (isset($idx['peso'])) {
                                                $novoPeso = trim($row[$idx['peso']]);

                                                if (in_array('peso', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                }
                                            }

                                            
                                            if (isset($idx['lista_cliente'])) {
                                                $lista_cliente = trim($row[$idx['lista_cliente']]);
                                                if (in_array(strtolower($lista_cliente), array("sim"))) {
                                                    $novaLista = "SIM";
                                                } else if (in_array(strtolower($lista_cliente), array("não", "nao"))) {
                                                    $novaLista = "NÃO";
                                                }
                                            }

                                            $gestao_mensal = '';
                                            if ($gestao_mensal_set = isset($idx['gestao_mensal'])) {
                                                if (in_array($gestao_mensal_set, array("SIM", "sim", "Sim", "S", "1"))) {
                                                    $gestao_mensal = 1;
                                                }elseif(in_array($gestao_mensal_set, array("NÃO", "não", "Não","NAO", "N", "0"))){
                                                    $gestao_mensal = 0;
                                                }
                                            }

                                            /* MAQUINA:
                                             * Campo opcional
                                             */
                                            if (isset($idx['maquina'])) {
                                                $novaMaquina = trim($row[$idx['maquina']]);

                                                if (in_array('maquina', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                }
                                            }

                                            /* ORIGEM:
                                             * Campo opcional
                                             */
                                            if (isset($idx['origem'])) {
                                                $novaOrigem = trim($row[$idx['origem']]);

                                                if (in_array('origem', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                }
                                            }

                                            /* PRIORIDADE:
                                             * Campo opcional
                                             */
                                            if (isset($idx['prioridade'])) {
                                                $novaPrioridade = trim($row[$idx['prioridade']]);

                                                if (in_array('prioridade', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                }
                                            }

                                            /* OBSERVAÇÕES:
                                             * Campo opcional
                                             */
                                            if (isset($idx['observacoes'])) {
                                                $novasObservacoes = trim($row[$idx['observacoes']]);

                                                if (in_array('observacoes', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                }
                                            }

                                            if (isset($idx['inf_adicionais'])) {
                                                $dbdata['inf_adicionais'] = formatar_texto($can_formatar_texto, trim($row[$idx['inf_adicionais']]));

                                                if (in_array('inf_adicionais', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                    $has_inf_adicionais = TRUE;
                                                }
                                            }


                                            /* APLICAÇÃO:
                                             * Campo opcional
                                             */
                                            if (isset($idx['aplicacao'])) {
                                                $dbdata['aplicacao'] = formatar_texto($can_formatar_texto, trim($row[$idx['aplicacao']]));

                                                if (in_array('aplicacao', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                    $has_aplicacao = TRUE;
                                                }
                                            }

                                            /* MARCA:
                                             * Campo opcional
                                             */
                                            if (isset($idx['marca'])) {
                                                $dbdata['marca'] = formatar_texto($can_formatar_texto, trim($row[$idx['marca']]));

                                                if (in_array('marca', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                    $has_marca = TRUE;
                                                }
                                            }


                                            /* MATERIAL CONSTITUTIVO:
                                             * Campo opcional
                                             */

                                            if (isset($idx['material_constitutivo'])) {
                                                $dbdata['material_constitutivo'] = isset($row[$idx['material_constitutivo']]) ? formatar_texto($can_formatar_texto, trim($row[$idx['material_constitutivo']])) : null;

                                                if (in_array('material_constitutivo', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                    $has_material_constitutivo = TRUE;
                                                }
                                            }

                                            /* DESCRIÇÃO PROPOSTA COMPLETA:
                                             * Campo opcional
                                             */
                                            $dbdata_item = array();

                                            if (isset($idx['descricao_proposta_completa'])) {
                                                $dbdata_item['descricao_proposta_completa'] = formatar_texto($can_formatar_texto, trim($row[$idx['descricao_proposta_completa']]));

                                                if (in_array('descricao_proposta_completa', $campos_adicionais)) {
                                                    $has_campo_adicional = TRUE;
                                                    $has_descricao_proposta_completa = TRUE;
                                                }
                                            }

                                            /* Regra de sempre homologar:
                                             */
                                            if (isset($idx['forcar_homologacao'])) {
                                                $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));
                                                
                                                if ($forcar == 'sim')
                                                {
                                                    $forcar_homologacao = TRUE;
                                                }
                                            }

               
                                            /* Código CEST */
                                            if (isset($idx['cod_cest'])) {
                                                $cod_cest = isset($row[$idx['cod_cest']]) ? trim($row[$idx['cod_cest']]) : null;
                                                $dbdata['cod_cest'] = null;

                                                if (!empty($cod_cest)) {
                                                    /**
                                                     * #4309 - CAMPO "COD_CEST" TABELA "CAD_ITEM"
                                                     * Para receber o parâmetro "N/D - Não atende" criamos essa condição
                                                     *  e mantemos compatibilidade com o valor -1 já utilizado no projeto
                                                     * -Andrei
                                                     */
                                                    if (preg_match("/(?:N\/D\s+?(?:\-|–))?(?:\s+)?N(?:ã|a|A|Ã)o\s+atende/i", $cod_cest)) {
                                                        $dbdata['cod_cest'] = '-1';
                                                    } else {
                                                        if ($this->cest_model->get_entry($cod_cest)) {
                                                            $dbdata['cod_cest'] = $cod_cest;
                                                        } else {
                                                            $log_com_erro['cest_erro'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                            $validar_erros++;
                                                        }
                                                    }
                                                }
                                            }

                                            /* Status Simplus */
                                            if ($empresa->integracao_simplus == 1 && isset($idx['status_simplus'])) {
                                                $status_simplus = mb_strtolower(trim($row[$idx['status_simplus']]));

                                                if (strcmp($status_simplus, 'sim') == 0) {
                                                    $dbdata['status_simplus'] = 0;
                                                }
                                            }

                                            if ((isset($validar_erros)) && $validar_erros > 0) {
                                                continue;
                                            }

                                            // cad_item
                                            $this->db->select(
                                                '
                                                ci.id_item,
                                                ci.part_number,
                                                ci.descricao_mercado_local,
                                                ci.subsidio,
                                                ci.caracteristicas,
                                                ci.memoria_classificacao,
                                                ci.houve_descricao_manual,
                                                ci.dispositivo_legal,
                                                ci.solucao_consulta,
                                                ci.funcao,
                                                ci.inf_adicionais,
                                                ci.aplicacao,
                                                ci.suframa_destaque,
                                                ci.num_ex_ii,
                                                ci.num_ex_ipi,
                                                ci.suframa_produto,
                                                ci.suframa_descricao,
                                                ci.suframa_codigo,
                                                ci.suframa_ppb,
                                                ci.inf_adicionais,
                                                ci.li,
                                                ci.li_orgao_anuente,
                                                ci.li_destaque,
                                                ci.antidumping,
                                                ci.houve_inf_adicionais_manual,
                                                ci.marca,
                                                ci.material_constitutivo,
                                                ci.houve_funcao_manual,
                                                ci.houve_aplicacao_manual,
                                                ci.houve_marca_manual,
                                                ci.houve_material_constitutivo_manual,
                                                ci.estabelecimento,
                                                ci.status_implementacao,
                                                ci.motivo_implementacao,
                                                ci.id_resp_fiscal,
                                                ci.id_resp_engenharia,
                                                gt.descricao as desc_grupo_tarifario,
                                                ci.id_grupo_tarifario,
                                                i.part_number_similar,
                                                i.peso,
                                                i.prioridade,
                                                i.observacoes,
                                                i.descricao_global,
                                                i.descricao_proposta_completa,
                                                i.houve_descricao_completa_manual,
                                                i.lista_cliente,
                                                i.evento,
                                                i.maquina,
                                                i.origem,
                                                s.slug,
                                                i.gestao_mensal'
                                            );

                                            $this->db->join('item i', 'i.part_number = ci.part_number AND i.id_empresa = ci.id_empresa AND i.estabelecimento = ci.estabelecimento', 'left');
                                            $this->db->join('grupo_tarifario gt', 'gt.id_grupo_tarifario = ci.id_grupo_tarifario', 'left');
                                            $this->db->join('status s', 's.id=i.id_status', 'inner');
                                            $query = $this->db->get_where(
                                                'cad_item ci',
                                                array(
                                                    'ci.part_number' => $part_number,
                                                    'ci.id_empresa'  => $id_empresa,
                                                    'ci.estabelecimento' => $estabelecimento
                                                )
                                            );

                                            $cad_item_total_rows = $query->num_rows();

                                            if ((isset($validar_erros)) && $validar_erros > 0) {
                                                continue;
                                            }

                                            if (!empty($dbdata['descricao_mercado_local'])) {
                                                $dbdata['dat_disp_homologacao'] = date('Y-m-d H:i:s');
                                            }

                                            $ncm_recomendada = null;
                                            if (isset($dbdata['ncm_proposto']) && !empty($dbdata['ncm_proposto'])) {
                                                $ncm_recomendada = $dbdata['ncm_proposto'];
                                            } else if ($cad_item_total_rows > 0) {
                                                $row_ncm = $query->row();
                                                $ncm_recomendada = isset($row_ncm->ncm_proposto) && !empty($row_ncm->ncm_proposto) ? $row_ncm->ncm_proposto : null;
                                            }

                                            // Limpar os campos de LI na cad_item para serem inseridos os novos LI, caso existam
                                            if ($cad_item_total_rows > 0 &&
                                                isset($idx['grupo_tarifario']) &&
                                                !empty($row[$idx['grupo_tarifario']]) &&
                                                !empty($ncm_recomendada)
                                                ) {
                                                $li = $this->ncm_model->get_li_oracle($ncm_recomendada);
                                                
                                                if (empty($li)) {
                                                    $dbdata_li = array();
                                                    $dbdata_li['li'] = 'NÃO';
                                                    $dbdata_li['li_orgao_anuente'] = '';
                                                    $dbdata_li['li_destaque'] = '';
    
                                                    $this->db->where('part_number', $part_number);
                                                    $this->db->where('id_empresa', $id_empresa);
                                                    $this->db->where('estabelecimento', $estabelecimento);
                                                    $this->db->update('cad_item', $dbdata_li);
                                                }
                                                    
                                            }

                                            //INSERINDO EX DE II E EX DE IPI CASO EXISTAM
                                            $ex_ii = null;
                                            $ex_ipi = null;

                                            if (isset($idx['ex_ii']) || isset($idx['ex_ipi']) && !empty($ncm_recomendada)) {
                                                if (isset($idx['ex_ii']) && !empty($row[$idx['ex_ii']])) {
                                                    $ex_ii = $this->ex_tarifario_model->get_ex_ii_by_ncm($row[$idx['ex_ii']], $ncm_recomendada);
                                                    if (isset($ex_ii->num_ex) && !empty($ex_ii->num_ex)) {
                                                        $dbdata['num_ex_ii'] = $ex_ii->num_ex;
                                                    } else {
                                                        $log_com_erro['num_ex_ii_inexistente'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                    }
                                                }

                                                if (isset($idx['ex_ipi']) && !empty($row[$idx['ex_ipi']]) && !empty($ncm_recomendada)) {
                                                    $ex_ipi = $this->ex_tarifario_model->get_ex_ipi_by_ncm($row[$idx['ex_ipi']], $ncm_recomendada);
                                                    if (isset($ex_ipi->num_ex) && !empty($ex_ipi->num_ex)) {
                                                        $dbdata['num_ex_ipi'] = $ex_ipi->num_ex;
                                                    } else {
                                                        $log_com_erro['num_ex_ipi_inexistente'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                    }
                                                }
                                            }

                                            //INSERINDO LI
                                            $li = null;
                                            if (isset($idx['li']) && !empty($row[$idx['li']]) && $row[$idx['li']] == 'SIM' && !empty($ncm_recomendada)) {

                                               $li = $this->ncm_model->getLiOracleParams($ncm_recomendada, $row[$idx['li_orgao_anuente']], $row[$idx['li_destaque']]);
                                                if (!empty($li) && !empty($li->ORGAO_ANUENTE)) {
                                                    $dbdata['li'] = 'SIM';
                                                    $dbdata['li_destaque'] = $row[$idx['li_destaque']];
                                                    $dbdata['li_orgao_anuente'] = $li->ORGAO_ANUENTE;
                                                } else {
                                                    $log_com_erro['li_inexistente'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                }
                                             }

                                            //INSERINDO SUFRAMA
                                            $suframa = null;
                                            if (isset($idx['suframa_descricao']) && !empty($row[$idx['suframa_descricao']]) && !empty($ncm_recomendada)) {
                                                $suframa = $this->suframa_model->getSuframaByDesc($ncm_recomendada, $row[$idx['suframa_descricao']], $row[$idx['suframa_codigo']]);
                                                if (!empty($suframa) && !empty($suframa->CODIGO) && !empty($suframa->DESTAQUE)) {
                                                    $dbdata['suframa_destaque'] = $suframa->DESTAQUE;
                                                    $dbdata['suframa_ppb'] = $suframa->PPB;
                                                    $dbdata['suframa_descricao'] = $suframa->DESCRICAO_SUFRAMA;
                                                    $dbdata['suframa_produto'] = $suframa->PRODUTO;
                                                    $dbdata['suframa_codigo'] = $suframa->CODIGO;
                                                } else {
                                                    $log_com_erro['suframa_inexistente'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                }
                                            }

                                            // Caso o item já exista na cad_item...
                                            if ($cad_item_total_rows > 0) {

                                                $item_row = $query->row();
                                                $dados_sistema = $query->result();
                                                $this->cad_item_model->atualiza_status_exportacao(0, array($item_row->id_item), $id_empresa);

                                                // Validação do id_grupo_tarifario. Caso o item não possua um grupo tarifário, o mesmo deve ser informado e o item não deve ser homologado.
                                                if (empty($item_row->id_grupo_tarifario)) {
                                                    $log_com_erro['item_nao_possui_grupo_tarifario'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                    continue;
                                                }

                                                if (!isset($idx['importado']) && $item_importado_default == true) {
                                                    $this->cad_item_model->define_item_importado($part_number, $estabelecimento, $id_empresa);
                                                } else if (isset($idx['importado']) && $row[$idx['importado']] == strtoupper('SIM')  ) {
                                                    $this->cad_item_model->define_item_importado($part_number, $estabelecimento, $id_empresa);
                                                } else if (isset($idx['importado']) && ($row[$idx['importado']] == strtoupper('NAO') || $row[$idx['importado']] == strtoupper('NÃO'))  ) {
                                                    $this->cad_item_model->remove_item_importado($part_number, $id_empresa, $estabelecimento);
                                                }
                                          
                                                if ($item_row->status_implementacao != 'R' && $item_row->status_implementacao != 'N') {
                                                    $log_com_erro['status_implementacao_nao_permitido'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                } else {
                                                    if (isset($gt) &&
                                                        isset($item_row->id_grupo_tarifario) &&
                                                        ($item_row->id_grupo_tarifario != $gt->id_grupo_tarifario))
                                                    {
                                                        $dbdata['num_ex_ii'] = NULL;
                                                        $dbdata['num_ex_ipi'] = NULL;

                                                        if (isset($ex_ii) && !empty($ex_ii)) {
                                                            $dbdata['num_ex_ii'] = $ex_ii->num_ex;
                                                        }

                                                        if (isset($ex_ipi) && !empty($ex_ipi)) {
                                                            $dbdata['num_ex_ipi'] = $ex_ipi->num_ex;
                                                        }


                                                        $this->db->where('id_item', $item_row->id_item);
                                                        $this->db->delete('cad_item_nve');
                                                    }

                                                    //valida e adiciona NVE se existir o que é necessário
                                                    $return_nve = $this->cad_item_nve_model->salvar_nve_planilha_homologacao($ncm_recomendada, $row, $idx, $item_row->id_item);

                                                    if (isset($return_nve->error) && $return_nve->error === TRUE) {
                                                        $log_com_erro['nve_inexistentes'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                    }

                                                    $this->db->select('u.nome');
                                                    $this->db->where('id_item', $item_row->id_item);
                                                    $this->db->where('homologado', 1);
                                                    $this->db->join('usuario u', 'u.id_usuario = ih.id_usuario', 'inner');

                                                    $query_homolog = $this->db->get('cad_item_homologacao ih');

                                                    $motivoLog = '';

                                                    if (isset($dbdata['antidumping']) && !empty($dbdata['antidumping']) && $item_row->antidumping != $dbdata['antidumping']) {
                                                        $motivoLog .= '<strong>ANTIDUMPING: </strong>' . $dbdata['antidumping'] . '<br>';
                                                    }

                                                    if (isset($dbdata['num_ex_ii']) && !empty($dbdata['num_ex_ii']) && $item_row->num_ex_ii != $dbdata['num_ex_ii']) {
                                                        $motivoLog .= '<strong>EX de II: </strong>' . $ex_ii->num_ex . ' - ' . $ex_ii->descricao_linha1 . '<br>';
                                                    }

                                                    if (isset($dbdata['num_ex_ipi']) && !empty($dbdata['num_ex_ipi']) && $item_row->num_ex_ipi != $dbdata['num_ex_ipi']) {
                                                        $motivoLog .= '<strong>EX de IPI: </strong>' . $ex_ipi->num_ex . ' - ' . $ex_ipi->descricao_linha1 . '<br>';
                                                    }

                                                    if (isset($dbdata['suframa_destaque']) && !empty($dbdata['suframa_destaque']) && $item_row->suframa_destaque != $dbdata['suframa_destaque']) {
                                                        $motivoLog .= '<strong>DESTAQUE SUFRAMA: </strong>' . $suframa->DESTAQUE . '<br>';
                                                    }
                                                    if (isset($dbdata['suframa_ppb']) && !empty($dbdata['suframa_ppb']) && $item_row->suframa_ppb != $dbdata['suframa_ppb']) {
                                                        $motivoLog .= '<strong>PPB SUFRAMA: </strong>' . $suframa->PPB . '<br>';
                                                    }
                                                    if (isset($dbdata['suframa_descricao']) && !empty($dbdata['suframa_descricao']) && $item_row->suframa_descricao != $dbdata['suframa_descricao']) {
                                                        $motivoLog .= '<strong>DESCRIÇÃO SUFRAMA: </strong>' . $suframa->DESCRICAO_SUFRAMA . '<br>';
                                                    }
                                                    if (isset($dbdata['suframa_produto']) && !empty($dbdata['suframa_produto']) && $item_row->suframa_produto != $dbdata['suframa_produto']) {
                                                        $motivoLog .= '<strong>PRODUTO SUFRAMA: </strong>' . $suframa->PRODUTO . '<br>';
                                                    }
                                                    if (isset($dbdata['suframa_codigo']) && !empty($dbdata['suframa_codigo']) && $item_row->suframa_codigo != $dbdata['suframa_codigo']) {
                                                        $motivoLog .= '<strong>CÓDIGO SUFRAMA: </strong>' . $suframa->CODIGO . '<br>';
                                                    }

                                                    if (isset($dbdata['li']) && !empty($dbdata['li']) && $item_row->li_destaque != $dbdata['li_destaque']) {
                                                        if ($dbdata['li'] == 'SIM') {
                                                            $motivoLog .= '<strong>LI: </strong>' . $dbdata['li'] . '<br>';
                                                            $motivoLog .= '<strong>DESTAQUE LI: </strong>' . $dbdata['li_destaque'] . '<br>';
                                                            $motivoLog .= '<strong>ORGÃO ANUENTE: </strong>' . $dbdata['li_orgao_anuente'] . '<br>';
                                                        } else {
                                                            $motivoLog .= '<strong>LI: </strong>' . $dbdata['li'] . '<br>';
                                                        }
                                                    }

                                                    if (isset($return_nve->logs) && !empty($return_nve->logs)) {
                                                        foreach ($return_nve->logs as $log) {
                                                            $motivoLog .= "<strong>NVE [{$log['nve_atributo']}]: </strong>" . $log['nve_valor'] . '<br>';
                                                        }
                                                    }

                                                    $motivoItem = "";

                                                    if ($item_row->peso != $novoPeso && !empty($novoPeso)) {
                                                        $motivoItem = "Alteração do Peso: <em>{$item_row->peso}</em> &rarr; <strong>{$novoPeso}</strong>". '<br>';
        
                                                    }

                                                    if (isset($dbdata['caracteristicas']) && !empty($dbdata['caracteristicas']) && $item_row->caracteristicas != $dbdata['caracteristicas']) {
                                                        $motivoItem .= "Alteração da Caracteristica: <em>{$item_row->caracteristicas}</em> &rarr; <strong>{$dbdata['caracteristicas']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['descricao_mercado_local']) && !empty($dbdata['descricao_mercado_local']) && $item_row->descricao_mercado_local != $dbdata['descricao_mercado_local']) {
                                                        $motivoItem .= "Alteração da Descrição Proposta Resumida: <em>{$item_row->descricao_mercado_local}</em> &rarr; <strong>{$dbdata['descricao_mercado_local']}</strong>". '<br>';
                                                    }
                                                    if (isset($gt) &&
                                                        isset($item_row->id_grupo_tarifario) &&
                                                        ($item_row->id_grupo_tarifario != $gt->id_grupo_tarifario)
                                                    ) {

                                                        $desc_antiga = $this->grupo_tarifario_model->get_entry($item_row->id_grupo_tarifario)->subsidio;
                                                        $desc_atual = $this->grupo_tarifario_model->get_entry($gt->id_grupo_tarifario)->subsidio;
                                                        $motivoItem .= "Alteração do Grupo: <em>{$desc_antiga}</em> &rarr; <strong>{$desc_atual}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['cod_owner']) && !empty($dbdata['cod_owner']) && $item_row->cod_owner != $dbdata['cod_owner']) {
                                                        $motivoItem .= "Alteração do Owner: <em>{$item_row->cod_owner}</em> &rarr; <strong>{$dbdata['cod_owner']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['subsidio']) && !empty($dbdata['subsidio']) && $item_row->subsidio != $dbdata['subsidio']) {
                                                        $motivoItem .= "Alteração do Subsidio: <em>{$item_row->subsidio}</em> &rarr; <strong>{$dbdata['subsidio']}</strong>". '<br>';
                                                    }
                                                    if (isset($dados_sistema) && isset($id_resp_fiscal)     && (reset($dados_sistema)->id_resp_fiscal     != $id_resp_fiscal)) {
                                                        $desc_antigo = is_array($dados_sistema) ? reset($dados_sistema)->id_resp_fiscal : $dados_sistema->id_resp_fiscal;
                                                        $desc_atual = $id_resp_fiscal;
                                                        
                                                        $desc_atual = $this->usuario_model->get_email_user($desc_atual);
                                                        $desc_antigo = $this->usuario_model->get_email_user($desc_antigo);
                                                        
                                                        $motivoItem .= "Alteração do Responsavel Fiscal: <em>{ $desc_antigo }</em> &rarr; <strong>{ $desc_atual }</strong>". '<br>';
                                                    }
                                                    if (isset($dados_sistema) && isset($id_resp_engenharia)     && (reset($dados_sistema)->id_resp_engenharia     != $id_resp_engenharia)) {
                                                        $desc_antigo = is_array($dados_sistema) ? reset($dados_sistema)->id_resp_engenharia : $dados_sistema->id_resp_engenharia;
                                                        $desc_atual = $id_resp_engenharia;
                                                        $desc_atual = $this->usuario_model->get_email_user($desc_atual);
                                                        $desc_antigo = $this->usuario_model->get_email_user($desc_antigo);

                                                        $motivoItem .= "Alteração do Responsavel de Engenharia: <em>{ $desc_antigo }</em> &rarr; <strong>{ $desc_atual }</strong>". '<br>';
                                                    }
 
                                                    if (isset($dbdata['memoria_classificacao']) && !empty($dbdata['memoria_classificacao']) && $item_row->memoria_classificacao != $dbdata['memoria_classificacao']) {
                                                        $motivoItem .= "Alteração da Memória de Classificação: <em>{$item_row->memoria_classificacao}</em> &rarr; <strong>{$dbdata['memoria_classificacao']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['evento']) && !empty($dbdata['evento']) && $item_row->evento != $dbdata['evento']) {
                                                        $motivoItem .= "Alteração do Evento: <em>{$item_row->evento}</em> &rarr; <strong>{$dbdata['evento']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['dispositivos_legais']) && !empty($dbdata['dispositivos_legais']) && $item_row->dispositivos_legais != $dbdata['dispositivos_legais']) {
                                                        $motivoItem .= "Alteração do Dispositivo Legal: <em>{$item_row->dispositivos_legais}</em> &rarr; <strong>{$dbdata['dispositivos_legais']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['solucao_consulta']) && !empty($dbdata['solucao_consulta']) && $item_row->solucao_consulta != $dbdata['solucao_consulta']) {
                                                        $motivoItem .= "Alteração da Solução de Consulta: <em>{$item_row->solucao_consulta}</em> &rarr; <strong>{$dbdata['solucao_consulta']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['funcao']) && !empty($dbdata['funcao']) && $item_row->funcao != $dbdata['funcao']) {
                                                        $motivoItem .= "Alteração da Função: <em>{$item_row->funcao}</em> &rarr; <strong>{$dbdata['funcao']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['peso']) && !empty($dbdata['peso']) && $item_row->peso != $dbdata['peso']) {
                                                        $motivoItem .= "Alteração do Peso: <em>{$item_row->peso}</em> &rarr; <strong>{$dbdata['peso']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['prioridade']) && !empty($dbdata['prioridade']) && $item_row->prioridade != $dbdata['prioridade']) {
                                                        $motivoItem .= "Alteração de Prioridade: <em>{$item_row->prioridade}</em> &rarr; <strong>{$dbdata['prioridade']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['inf_adicionais']) && !empty($dbdata['inf_adicionais']) && $item_row->inf_adicionais != $dbdata['inf_adicionais']) {
                                                        $motivoItem .= "Alteração de Informações Adicionais: <em>{$item_row->inf_adicionais}</em> &rarr; <strong>{$dbdata['inf_adicionais']}</strong>". '<br>';
                                                    }

                                                    if (isset($dbdata['aplicacao']) && !empty($dbdata['aplicacao']) && $item_row->aplicacao != $dbdata['aplicacao']) {
                                                        $motivoItem .= "Alteração de Aplicação: <em>{$item_row->aplicacao}</em> &rarr; <strong>{$dbdata['aplicacao']}</strong>". '<br>';
                                                    }

                                                    if (isset($dbdata['marca']) && !empty($dbdata['marca']) && $item_row->marca != $dbdata['marca']) {
                                                        $motivoItem .= "Alteração de Marca: <em>{$item_row->marca}</em> &rarr; <strong>{$dbdata['marca']}</strong>". '<br>';
                                                    }

                                                    if (isset($dbdata['descricao_proposta_completa']) && !empty($dbdata['descricao_proposta_completa']) && $item_row->descricao_proposta_completa != $dbdata['descricao_proposta_completa']) {
                                                        $motivoItem .= "Alteração de Descrição Proposta Completa: <em>{$item_row->descricao_proposta_completa}</em> &rarr; <strong>{$dbdata['descricao_proposta_completa']}</strong>". '<br>';
                                                    }

                                                    if (isset($dbdata['descricao_proposta_resumida']) && !empty($dbdata['descricao_proposta_resumida']) && $item_row->descricao_mercado_local != $dbdata['descricao_proposta_resumida']) {
                                                        $motivoItem .= "Alteração de Descrição Proposta Resumida: <em>{$item_row->descricao_mercado_local}</em> &rarr; <strong>{$dbdata['descricao_proposta_resumida']}</strong>". '<br>';
                                                    }

                                                    if (isset($dbdata['material_constitutivo']) && !empty($dbdata['material_constitutivo']) && $item_row->material_constitutivo != $dbdata['material_constitutivo']) {
                                                        $motivoItem .= "Alteração de Material Constitutivo: <em>{$item_row->material_constitutivo}</em> &rarr; <strong>{$dbdata['material_constitutivo']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['cod_cest']) && !empty($dbdata['cod_cest']) && $item_row->cod_cest != $dbdata['cod_cest']) {
                                                        $motivoItem .= "Alteração de Cest: <em>{$item_row->cod_cest}</em> &rarr; <strong>{$dbdata['cod_cest']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['status_simplus']) && !empty($dbdata['status_simplus']) && $item_row->status_simplus != $dbdata['status_simplus']) {
                                                        $motivoItem .= "Alteração de Simplus: <em>{$item_row->status_simplus}</em> &rarr; <strong>{$dbdata['status_simplus']}</strong>". '<br>';
                                                    }
                                                    if (isset($dbdata['estabelecimento']) && !empty($dbdata['estabelecimento']) && $item_row->estabelecimento != $dbdata['estabelecimento']) {
                                                        $motivoItem .= "Alteração de Estabelecimento: <em>{$item_row->estabelecimento}</em> &rarr; <strong>{$dbdata['estabelecimento']}</strong>". '<br>';
                                                    }                                    

                                                    if ($item_row->lista_cliente != $novaLista && !empty($novaLista)) {
                                                        $motivoItem .= "Alteração da Lista: <em>{$item_row->lista_cliente}</em> &rarr; <strong>{$novaLista}</strong>". '<br>';
        
                                                    }

                                                    if ($item_row->prioridade != $novaPrioridade && !empty($novaPrioridade)) {
                                                        $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
                                                        $motivoItem .= "Alteração da Prioridade: <em>{$item_row->prioridade}</em> &rarr; <strong>{$novaPrioridade}</strong>". '<br>';
                                                    }

                                                    if ($item_row->maquina != $novaMaquina && !empty($novaMaquina)) {
                                                        $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
                                                        $motivoItem .= "Alteração da Maquina: <em>{$item_row->maquina}</em> &rarr; <strong>{$novaMaquina}</strong>". '<br>';
                                                    }

                                                    if ($item_row->origem != $novaOrigem && !empty($novaOrigem)) {
                                                        $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
                                                        $motivoItem .= "Alteração da Origem: <em>{$item_row->origem}</em> &rarr; <strong>{$novaOrigem}</strong>". '<br>';
                                                    }
                                                    
                                                    if ($item_row->observacoes != $novasObservacoes && !empty($novasObservacoes)) {
                                                        $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
                                                        $motivoItem .= "Alteração das Observações: <em>{$item_row->observacoes}</em> &rarr;$ <strong>{$novasObservacoes}</strong>". '<br>';
                                                    }

                                                    // Gestão Mensal
                                                    if (isset($gestao_mensal) && isset($item_row->gestao_mensal) && $item_row->gestao_mensal != $gestao_mensal) {
                                                        $motivoItem .= "Alteração da Gestão Mensal: <em>" . ($item_row->gestao_mensal == 1 ? 'Sim' : 'Não') . "</em> &rarr; <strong>" . ($gestao_mensal == 1 ? 'Sim' : 'Não') . "</strong><br>";
                                                    }

                                                    $itemUpdate = array();

                                                    if (!empty($ncm_recomendada) && in_array('lessin', $funcoes_adicionais)) {
                                                        $exII = false;
                                                        if (isset($dbdata['num_ex_ii']) && !empty($dbdata['num_ex_ii'])) {
                                                            if ($item_row->num_ex_ii != $dbdata['num_ex_ii']) {
                                                                $exII = $dbdata['num_ex_ii'];
                                                            } else {
                                                                $exII = $item_row->num_ex_ii;
                                                            }
                                                        }

                                                        try {
                                                            $utilizaLessin = $this->lessin_model->utiliza_lessin(array('ncm' => $ncm_recomendada), $exII);
                                                            
                                                            $motivoItem .= !empty($motivoItem) ? "<br />" : "";
                                                            
                                                            $itemUpdate['id_lessin'] = $utilizaLessin['id'];
                                                            $itemUpdate['regra_aplicada'] = $utilizaLessin['regra'];
                                                            $itemUpdate['lista_becomex'] = isset($utilizaLessin['utiliza_lessin']) && $utilizaLessin['utiliza_lessin'] ? 'SIM' : 'NÃO';
    
                                                            $motivoItem .= "Atualização da LESSIN: Lista Becomex de <em>{$item_row->lista_becomex}</em> para <strong>{$itemUpdate['lista_becomex']}</strong>; Lista cliente de <em>{$item_row->lista_cliente}</em> para <strong>{$novaLista}</strong>.";
                                                        } catch(Exception $e) {
                                                            $log_com_erro['item_homologado'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                        }
                                                    }
                                                    $force = (isset($idx['forcar_atualizacao']) ? mb_strtolower(trim($row[$idx['forcar_atualizacao']])) : 'nao');
                                                   
                                                    if ($force != "sim") {
                                                        if (isset($idx['descricao_proposta_resumida']) && empty(trim($idx['descricao_proposta_resumida'])) )
                                                        {
                                                            $log_com_erro['excecao_erro'][] = array('part_number' => $part_number, 'message' => 'Descrição da Proposta Resumida está vazia');
                                                            break;
                                                        }
                                                    }
                          
                                                    if (!empty($motivoItem)) {
                                                        if ($force == "sim") {
                                                            $itemUpdate["peso"] = !empty($novoPeso) ? $novoPeso : $item_row->peso;
                                                            $itemUpdate["lista_cliente"] = !empty($novaLista) ? $novaLista : $item_row->lista_cliente;
                                                            $itemUpdate["prioridade"] = !empty($novaPrioridade) ? $novaPrioridade : $item_row->prioridade;
                                                            $itemUpdate["observacoes"] = !empty($novasObservacoes) ? $novasObservacoes : $item_row->observacoes;
                                                            $itemUpdate["maquina"] = !empty($novaMaquina) ? $novaMaquina : $item_row->maquina; 
                                                            $itemUpdate["origem"] = !empty($novaOrigem) ? $novaOrigem : $item_row->origem; 
                                                            
                                                            if (!empty($gestao_mensal))
                                                            {
                                                                $itemUpdate["gestao_mensal"] = $gestao_mensal;
                                                            }
                                                            $this->item_model->update_item($part_number, $id_empresa, $itemUpdate, $motivoItem, $estabelecimento);
                                                        }
                                                    }
                                                 
                                                    if (!empty($motivoLog)) {
                                                        $log = array(
                                                            'part_number'       => $part_number,
                                                            'estabelecimento'   => $estabelecimento,
                                                            'tipo_homologacao'  => 'Engenharia',
                                                            'id_usuario'        => sess_user_id(),
                                                            'id_empresa'        => $id_empresa,
                                                            'titulo'            => 'reenvio',
                                                            'criado_em'         => date("Y-m-d H:i:s"),
                                                            'motivo'            => $motivoLog
                                                        );
                                                        $this->item_log_model->save($log);
                                                    }


                                                    // Caso já haja homologação
                                                    if ($query_homolog->num_rows() > 0) {
                                                    //    $force = (isset($idx['forcar_atualizacao']) ? mb_strtolower(trim($row[$idx['forcar_atualizacao']])) : 'nao');

                                                        if ($force != "sim") {
                                                            $homolog_rscs = '';

                                                            foreach ($query_homolog->result() as $result_homolog) {
                                                                if (!empty($homolog_rscs))
                                                                    $homolog_rscs .= ", ";

                                                                $homolog_rscs .= '<strong>' . $result_homolog->nome . '</strong>';
                                                            }

                                                            $log_com_erro['item_homologado'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                        } else {
                                                            $item_similar_row = $query->row();

                                                            if (
                                                                isset($gt) && !empty($item_row->part_number_similar) &&
                                                                isset($item_similar_row->id_grupo_tarifario) &&
                                                                $item_similar_row->id_grupo_tarifario != $id_grupo_tarifario
                                                            ) {
                                                                $log_com_erro['troca_grupo_tarifario_erro'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                            } else {
                                                                if ($validar === TRUE) {
                                                                    // Verfica somente se a descrição do item foi alterada.
                                                                    if (isset($idx['descricao_proposta_resumida']) && ($item_row->descricao_mercado_local != $dbdata['descricao_mercado_local'])) {
                                                                        $validar_descricao_item[] = array(
                                                                            'item'                       => $item_row,
                                                                            'nova_descricao_sugerida'    => $dbdata['descricao_mercado_local'],
                                                                            'novo_subsidio'              => $dbdata['subsidio'],
                                                                            'nova_caracteristica'        => $dbdata['caracteristicas'],
                                                                            'nova_memoria_classificacao' => $dbdata['memoria_classificacao'],
                                                                            'novo_dispositivo_legal'     => $dbdata['dispositivo_legal'],
                                                                            'nova_solucao_consulta'      => $dbdata['solucao_consulta']
                                                                        );
                                                                    }

                                                                    if (isset($gt) &&
                                                                        isset($item_row->id_grupo_tarifario) &&
                                                                        ($id_grupo_tarifario !== $item_row->id_grupo_tarifario))
                                                                    {
                                                                        // Caso tenha alteração de Grupo Tarifário, locar itens na matriz para validação posterior.
                                                                        $novo_grupo_tarifario = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);

                                                                        $validar_grupo_tarifario[] = array(
                                                                            'item' => $item_row,
                                                                            'novo_grupo_tarifario' => $novo_grupo_tarifario
                                                                        );
                                                                    }

                                                                    if ($has_campo_adicional === TRUE) {
                                                                        if ($has_funcao === TRUE) {
                                                                            if (array_key_exists('funcao', $dbdata) && $item_row->funcao !== $dbdata['funcao']) {
                                                                                $validar_funcao_aplicacao_marca[$vfapm]['nova_funcao'] = formatar_texto($can_formatar_texto, $dbdata['funcao']);
                                                                                unset($dbdata['funcao']);
                                                                            }
                                                                        }

                                                                        if ($has_inf_adicionais === TRUE) {
                                                                            if (array_key_exists('inf_adicionais', $dbdata) && $item_row->inf_adicionais !== $dbdata['inf_adicionais']) {
                                                                                $validar_funcao_aplicacao_marca[$vfapm]['inf_adicionais'] = formatar_texto($can_formatar_texto, $dbdata['inf_adicionais']);
                                                                                unset($dbdata['inf_adicionais']);
                                                                            }
                                                                        }

                                                                        if ($has_aplicacao === TRUE) {
                                                                            if (array_key_exists('aplicacao', $dbdata) && $item_row->aplicacao !== $dbdata['aplicacao']) {
                                                                                $validar_funcao_aplicacao_marca[$vfapm]['nova_aplicacao'] = formatar_texto($can_formatar_texto, $dbdata['aplicacao']);
                                                                                unset($dbdata['aplicacao']);
                                                                            }
                                                                        }

                                                                        if ($has_marca === TRUE) {
                                                                            if (array_key_exists('marca', $dbdata) && $item_row->marca !== $dbdata['marca']) {
                                                                                $validar_funcao_aplicacao_marca[$vfapm]['nova_marca'] = formatar_texto($can_formatar_texto, $dbdata['marca']);
                                                                                unset($dbdata['marca']);
                                                                            }
                                                                        }

                                                                        if ($has_material_constitutivo === TRUE) {
                                                                            if (array_key_exists('material_constitutivo', $dbdata) && $item_row->material_constitutivo !== $dbdata['material_constitutivo']) {
                                                                                $validar_funcao_aplicacao_marca[$vfapm]['novo_material_constitutivo'] = formatar_texto($can_formatar_texto, $dbdata['material_constitutivo']);
                                                                                unset($dbdata['material_constitutivo']);
                                                                            }
                                                                        }

                                                                        if ($has_descricao_proposta_completa === TRUE) {
                                                                            if (array_key_exists('descricao_proposta_completa', $dbdata_item) && $item_row->descricao_proposta_completa !== $dbdata_item['descricao_proposta_completa']) {
                                                                                $validar_funcao_aplicacao_marca[$vfapm]['nova_descricao_completa'] = formatar_texto($can_formatar_texto, $dbdata_item['descricao_proposta_completa']);
                                                                                unset($dbdata_item['descricao_proposta_completa']);
                                                                            } else {
                                                                                $this->item_model->save(array('descricao_proposta_completa' => $dbdata_item['descricao_proposta_completa']), array('part_number' => $part_number, 'id_empresa' => $id_empresa, 'estabelecimento' => $estabelecimento));
                                                                            }
                                                                        }

                                                                        if (isset($validar_funcao_aplicacao_marca) && count($validar_funcao_aplicacao_marca[$vfapm]) > 0) {
                                                                            $validar_funcao_aplicacao_marca[$vfapm]['item'] = $item_row;
                                                                            $validar_funcao_aplicacao_marca[$vfapm]['xlsdata'] = array_merge($dbdata, $dbdata_item);
                                                                            $validar_funcao_aplicacao_marca[$vfapm]['id_empresa'] = $id_empresa;
                                                                            $validar_funcao_aplicacao_marca[$vfapm]['has_funcao'] = $has_funcao;
                                                                            $validar_funcao_aplicacao_marca[$vfapm]['has_inf_adicionais'] = $has_inf_adicionais;
                                                                            $validar_funcao_aplicacao_marca[$vfapm]['has_aplicacao'] = $has_aplicacao;
                                                                            $validar_funcao_aplicacao_marca[$vfapm]['has_marca'] = $has_marca;
                                                                            $validar_funcao_aplicacao_marca[$vfapm]['has_material_constitutivo'] = $has_material_constitutivo;
                                                                            $validar_funcao_aplicacao_marca[$vfapm]['has_descricao_proposta_completa'] = $has_descricao_proposta_completa;

                                                                            $vfapm++;
                                                                        }
                                                                    }

                                                                    if ((isset($validar_grupo_tarifario)) && count($validar_grupo_tarifario) > 0) {
                                                                        unset($dbdata['id_grupo_tarifario']);
                                                                    }

                                                                    if ((isset($validar_descricao_item)) && count($validar_descricao_item) > 0) {
                                                                        unset($dbdata['descricao_mercado_local']);
                                                                    }
                                                                }
                                                                $old_item = $this->cad_item_model->get_entry($item_row->id_item);

                                                                if (isset($dbdata['cod_cest']) && !empty($dbdata['cod_cest'])) {
                                                                    if ($dbdata['cod_cest'] != $old_item->cod_cest_proposto) {
                                                                        $this->load->model('cest_model');
                                                                        $cest = $this->cest_model->get_entry($dbdata['cod_cest']);
                                                                        $descricao = "<strong>CEST:</strong> {$cest->cod_cest}
                                                                        <br /><strong>Descrição:</strong> {$cest->descricao}";
                                                                        $log_data['part_number'] = $part_number;
                                                                        $log_data['estabelecimento'] = $estabelecimento;
                                                                        $log_data['id_usuario'] = sess_user_id();
                                                                        $log_data['id_empresa'] = sess_user_company();
                                                                        $log_data['criado_em'] = date('Y-m-d H:i:s');
                                                                        $log_data['titulo'] = 'vinculacao_cest_novo';
                                                                        $motivo_log = '<strong>CEST Vinculado</strong><br />' . $descricao;
                                                                        $log_data['motivo'] = $motivo_log;
                                                                        $this->item_log_model->save($log_data);
                                                                        $this->item_cest_model->insert_item_log($part_number, sess_user_company(), $estabelecimento, array(), $motivo_log);
                                                                    }
                                                                } else {
                                                                    if (!empty($old_item->cod_cest_proposto) && $old_item->cod_cest_proposto) {
                                                                        $log_data['part_number'] = $part_number;
                                                                        $log_data['estabelecimento'] = $estabelecimento;
                                                                        $log_data['id_usuario'] = sess_user_id();
                                                                        $log_data['id_empresa'] = sess_user_company();
                                                                        $log_data['criado_em'] = date('Y-m-d H:i:s');
                                                                        $log_data['titulo'] = 'vinculacao_cest_remove';
                                                                        $motivo_log = '<strong>CEST desvinculado</strong>';
                                                                        $log_data['motivo'] = $motivo_log;
                                                                        $this->item_log_model->save($log_data);
                                                                        $this->item_cest_model->insert_item_log($part_number, sess_user_company(), $estabelecimento, array(), $motivo_log);
                                                                    }
                                                                }
                                                                $this->db->update('cad_item', $dbdata, array('id_item' => $item_row->id_item));
                                                                $log_atualizados[] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);

                                                                if (isset($idx['forcar_homologacao'])) {
                                                                    $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));
                                                                    
                                                                    if ($forcar == 'sim')
                                                                    {
                                                                        $forcar_homologacao = TRUE;
                                                                    }
                                                                }
                                                                
                                                                if ($forcar_homologacao)
                                                                {
                                                                    $dbdata2 = array(
                                                                        'part_number'       => $part_number,
                                                                        'estabelecimento'   => $estabelecimento,
                                                                        'tipo_homologacao'  => 'Engenharia',
                                                                        'id_usuario'        => sess_user_id(),
                                                                        'id_empresa'        => $id_empresa,
                                                                        'titulo'            => 'reenvio',
                                                                        'criado_em'         => date("Y-m-d H:i:s"),
                                                                        'motivo'            => 'Item disponibilizado para homologação'
                                                                    );
    
                                                                    $this->item_log_model->save($dbdata2);

                                                                }
                                                            }
                                                        }
                                                    } else {
                                                        if ($validar === TRUE) {
                                                            if ($has_campo_adicional === TRUE) {
                                                                if ($has_funcao === TRUE) {
                                                                    if (array_key_exists('funcao', $dbdata) && $item_row->funcao !== $dbdata['funcao'] && ($item_row->houve_funcao_manual == 1)) {
                                                                        $validar_funcao_aplicacao_marca[$vfapm]['nova_funcao'] = formatar_texto($can_formatar_texto, $dbdata['funcao']);
                                                                        unset($dbdata['funcao']);
                                                                    }
                                                                }

                                                                if ($has_inf_adicionais === TRUE) {
                                                                    if (array_key_exists('inf_adicionais', $dbdata) && $item_row->inf_adicionais !== $dbdata['inf_adicionais'] && ($item_row->houve_inf_adicionais_manual == 1)) {
                                                                        $validar_funcao_aplicacao_marca[$vfapm]['nova_inf_adicionais'] = formatar_texto($can_formatar_texto, $dbdata['inf_adicionais']);
                                                                        unset($dbdata['inf_adicionais']);
                                                                    }
                                                                }

                                                                if ($has_aplicacao === TRUE) {
                                                                    if (array_key_exists('aplicacao', $dbdata) && $item_row->aplicacao !== $dbdata['aplicacao'] && ($item_row->houve_aplicacao_manual == 1)) {
                                                                        $validar_funcao_aplicacao_marca[$vfapm]['nova_aplicacao'] = formatar_texto($can_formatar_texto, $dbdata['aplicacao']);
                                                                        unset($dbdata['aplicacao']);
                                                                    }
                                                                }

                                                                if ($has_marca === TRUE) {
                                                                    if (array_key_exists('marca', $dbdata) && $item_row->marca !== $dbdata['marca'] && ($item_row->houve_marca_manual == 1)) {
                                                                        $validar_funcao_aplicacao_marca[$vfapm]['nova_marca'] = formatar_texto($can_formatar_texto, $dbdata['marca']);
                                                                        unset($dbdata['marca']);
                                                                    }
                                                                }

                                                                if ($has_material_constitutivo === TRUE) {
                                                                    if (array_key_exists('material_constitutivo', $dbdata) && $item_row->material_constitutivo !== $dbdata['material_constitutivo'] && ($item_row->houve_material_constitutivo_manual == 1)) {
                                                                        $validar_funcao_aplicacao_marca[$vfapm]['novo_material_constitutivo'] = formatar_texto($can_formatar_texto, $dbdata['material_constitutivo']);
                                                                        unset($dbdata['material_constitutivo']);
                                                                    }
                                                                }

                                                                if ($has_descricao_proposta_completa === TRUE) {
                                                                    if (array_key_exists('descricao_proposta_completa', $dbdata_item) && $item_row->descricao_proposta_completa !== $dbdata_item['descricao_proposta_completa'] && ($item_row->houve_descricao_completa_manual == 1)) {
                                                                        $validar_funcao_aplicacao_marca[$vfapm]['nova_descricao_completa'] = formatar_texto($can_formatar_texto, $dbdata_item['descricao_proposta_completa']);
                                                                        unset($dbdata_item['descricao_proposta_completa']);
                                                                    } else {
                                                                        $this->item_model->save(array('descricao_proposta_completa' => $dbdata_item['descricao_proposta_completa']), array('part_number' => $part_number, 'id_empresa' => $id_empresa, 'estabelecimento' => $estabelecimento));
                                                                    }
                                                                }

                                                                if (isset($validar_funcao_aplicacao_marca[$vfapm]) && count($validar_funcao_aplicacao_marca[$vfapm]) > 0) {
                                                                    $validar_funcao_aplicacao_marca[$vfapm]['item'] = $item_row;
                                                                    $validar_funcao_aplicacao_marca[$vfapm]['xlsdata'] = array_merge($dbdata, $dbdata_item);
                                                                    $validar_funcao_aplicacao_marca[$vfapm]['id_empresa'] = $id_empresa;
                                                                    $validar_funcao_aplicacao_marca[$vfapm]['has_funcao'] = $has_funcao;
                                                                    $validar_funcao_aplicacao_marca[$vfapm]['has_inf_adicionais'] = $has_inf_adicionais;
                                                                    $validar_funcao_aplicacao_marca[$vfapm]['has_aplicacao'] = $has_aplicacao;
                                                                    $validar_funcao_aplicacao_marca[$vfapm]['has_marca'] = $has_marca;
                                                                    $validar_funcao_aplicacao_marca[$vfapm]['has_material_constitutivo'] = $has_material_constitutivo;
                                                                    $validar_funcao_aplicacao_marca[$vfapm]['has_descricao_proposta_completa'] = $has_descricao_proposta_completa;

                                                                    $vfapm++;
                                                                }
                                                            }

                                                            if (
                                                                isset($idx['descricao_proposta_resumida']) &&
                                                                ($item_row->houve_descricao_manual == 1 && $item_row->descricao_mercado_local !== $dbdata['descricao_mercado_local'])
                                                            ) {
                                                                $item_row2 = $item_row;

                                                                $validar_descricao_item[] = array(
                                                                    'item'                       => $item_row2,
                                                                    'nova_descricao_sugerida'    => $dbdata['descricao_mercado_local'],
                                                                    'novo_subsidio'              => $dbdata['subsidio'],
                                                                    'nova_caracteristica'        => $dbdata['caracteristicas'],
                                                                    'nova_memoria_classificacao' => $dbdata['memoria_classificacao'],
                                                                    'novo_dispositivo_legal'     => $dbdata['dispositivo_legal'],
                                                                    'nova_solucao_consulta'      => $dbdata['solucao_consulta']
                                                                );

                                                                unset($dbdata['descricao_mercado_local']);
                                                            }
                                                        }

                                                        if (isset($dbdata['cod_cest']) && !empty($dbdata['cod_cest'])) {
                                                            if ($dbdata['cod_cest'] != $old_item->cod_cest_proposto) {

                                                                $cest = $this->cest_model->get_entry($dbdata['cod_cest']);
                                                                $descricao = "<strong>CEST:</strong> {$cest->cod_cest}
                                                                <br /><strong>Descrição:</strong> {$cest->descricao}";
                                                                $log_data['part_number'] = $part_number;
                                                                $log_data['estabelecimento'] = $estabelecimento;
                                                                $log_data['id_usuario'] = sess_user_id();
                                                                $log_data['id_empresa'] = sess_user_company();
                                                                $log_data['criado_em'] = date('Y-m-d H:i:s');
                                                                $log_data['titulo'] = 'vinculacao_cest_novo';
                                                                $motivo_log = '<strong>CEST Vinculado</strong><br />' . $descricao;
                                                                $log_data['motivo'] = $motivo_log;
                                                                $this->item_log_model->save($log_data);
                                                                $this->item_cest_model->insert_item_log($part_number, sess_user_company(), $estabelecimento, array(), $motivo_log);
                                                            }
                                                        } else {
                                                            if (!empty($old_item->cod_cest_proposto) && $old_item->cod_cest_proposto) {
                                                                $log_data['part_number'] = $part_number;
                                                                $log_data['estabelecimento'] = $estabelecimento;
                                                                $log_data['id_usuario'] = sess_user_id();
                                                                $log_data['id_empresa'] = sess_user_company();
                                                                $log_data['criado_em'] = date('Y-m-d H:i:s');
                                                                $log_data['titulo'] = 'vinculacao_cest_remove';
                                                                $motivo_log = '<strong>CEST desvinculado</strong>';
                                                                $log_data['motivo'] = $motivo_log;
                                                                $this->item_log_model->save($log_data);
                                                                $this->item_cest_model->insert_item_log($part_number, sess_user_company(), $estabelecimento, array(), $motivo_log);
                                                            }
                                                        }
                                                        if ($force == "sim") {
                                                   
                                                            if (has_role('sysadmin')) {
                                                                

                                                                // $this->db->query("SET @user_update_cad_item_homologacao = " . sess_user_id());
                                                                $this->db->update('cad_item', $dbdata, array('id_item' => $item_row->id_item));
                                                                // $this->db->query("SET @user_update_cad_item_homologacao = NULL");
                                                            } else {
                                                                $this->db->update('cad_item', $dbdata, array('id_item' => $item_row->id_item));
                                                            }
                     
                                                        }

                                                        $log_atualizados[] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                        if (isset($idx['forcar_homologacao'])) {
                                                            $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));
                                                            
                                                            if ($forcar == 'sim')
                                                            {
                                                                $forcar_homologacao = TRUE;
                                                            }
                                                        }
                                                        
                                                        if ($forcar_homologacao)
                                                        {
                                                            $dbdata2 = array(
                                                                'part_number'       => $part_number,
                                                                'estabelecimento'   => $estabelecimento,
                                                                'tipo_homologacao'  => 'Engenharia',
                                                                'id_usuario'        => sess_user_id(),
                                                                'id_empresa'        => $id_empresa,
                                                                'titulo'            => 'reenvio',
                                                                'criado_em'         => date("Y-m-d H:i:s"),
                                                                'motivo'            => 'Item disponibilizado para homologação'
                                                            );

                                                            $this->item_log_model->save($dbdata2);
                                                        }
                                                    }
                                                }

                                                // ***** NOVAS REGRAS DE MUDANÇA DE STATUS E GRUPO TARIFARIO *****
                                                
                                                $homologar = false;
                                                if (!empty($idx['grupo_tarifario'])) {

                                                    if ($item_row->slug == 'homologar') {
                                                        $this->db->where('id_item', $item_row->id_item);
                                                        $query = $this->db->get('cad_item_homologacao');

                                                        if ($query->num_rows() > 0) {
                                                            $homologar = true;
                                                        }

                                                    } elseif (
                                                        $item_row->slug == 'homologado' ||
                                                        $item_row->slug == 'nao_homologado' ||
                                                        $item_row->slug == 'revisao' ||
                                                        $item_row->slug == 'em_analise' ||
                                                        $item_row->slug == 'pendente_duvidas' ||
                                                        $item_row->slug == 'respondido'
                                                    ) {
                                                        $homologar = true;
                                                    }
                                                }

                                                // $dados_sistema = $query->result();

                                                if (
                                                    isset($item_row) && $item_row->slug != 'obsoleto' && (
                                                        (empty($item_row->descricao_mercado_local) &&
                                                         isset($idx['descricao_proposta_resumida']) &&
                                                         !empty($row[$idx['descricao_proposta_resumida']]))
                                                      || (isset($item_row->descricao_mercado_local) &&
                                                          isset($idx['descricao_proposta_resumida']) &&
                                                          ($item_row->descricao_mercado_local != $row[$idx['descricao_proposta_resumida']]))
                                                      || (isset($dados_sistema) &&
                                                          isset($id_resp_engenharia) &&
                                                          (reset($dados_sistema)->id_resp_engenharia != $id_resp_engenharia))
                                                      || (isset($dados_sistema) &&
                                                          isset($id_resp_fiscal) &&
                                                          (reset($dados_sistema)->id_resp_fiscal != $id_resp_fiscal))
                                                      || (!empty($gt) &&
                                                          $gt->id_grupo_tarifario != $dados_sistema[0]->id_grupo_tarifario)
                                                    )
                                                ) {
                                                    $homologar = true;
                                                }

                                                if (
                                                    isset($gt, $dados_sistema, $gt->id_grupo_tarifario)
                                                    && $gt->id_grupo_tarifario != $dados_sistema[0]->id_grupo_tarifario
                                                ) {
                                                    $homologar = true;
                                                }
                                        
                                                if ($forcar_homologacao == true)
                                                {
                                                    $homologar = true;

                                                    $log = array(
                                                        'part_number'       => $dbdata['part_number'],
                                                        'estabelecimento'   => $dbdata['estabelecimento'],
                                                        'tipo_homologacao'  => 'Engenharia',
                                                        'id_usuario'        => sess_user_id(),
                                                        'id_empresa'        => $id_empresa,
                                                        'titulo'            => 'envio',
                                                        'criado_em'         => date("Y-m-d H:i:s"),
                                                        'motivo'            => '<strong> Envio forçado para homologação via flag </strong>' . '<br>'
                                                    );
                                                    $this->item_log_model->save($log);
                                                }

                                                $forcar_mudanca = false;
                                                if (isset($idx['forcar_homologacao'])) {
                                                    $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));
                                                    
                                                    if ($forcar == 'sim')
                                                    {
                                                        $forcar_mudanca = true;
                                                    }
                                                }

                                                if ($homologar == true || $forcar_mudanca == true )
                                                {
                                                    $this->atualizar_status(
                                                        $item_base,
                                                        $dbdata['part_number'],
                                                        $dbdata['estabelecimento'],
                                                        $id_empresa,
                                                        $forcar_mudanca,
                                                        'atualizar',
                                                        $item_row->id_item,
                                                        $homologar,
                                                        (!empty($gt) && !empty($dados_sistema[0]) &&
                                                            $gt->id_grupo_tarifario != $dados_sistema[0]->id_grupo_tarifario ) ?
                                                            true :
                                                            false);

                                                    // if ((!empty($item_row->id_item) && $this->verifica_status_homologados_reprovados($item_base, $part_number, $estabelecimento, $id_empresa, (!empty($gt) && $gt->id_grupo_tarifario != $dados_sistema[0]->id_grupo_tarifario ) ? TRUE : FALSE)) || $forcar_mudanca == TRUE)
                                                    // {
                                                    //     $this->cad_item_homologacao_model->drop_item($item_row->id_item);
                                                    // } 
                                                  
                                                }
                                            } else {
                                                $prioridade_atual_item = $this->empresa_prioridades_model->get_old_prioridade($part_number,$id_empresa,$estabelecimento);
                                                $old_prioridade = $prioridade_atual_item[0]->prioridade_atual;
                                                if (!empty($dbdata['descricao_mercado_local']))
                                                {
                                                    if ($has_descricao_proposta_completa === TRUE) {
                                                        $this->item_model->save(array('descricao_proposta_completa' => $dbdata_item['descricao_proposta_completa']), array('part_number' => $part_number, 'id_empresa' => $id_empresa, 'estabelecimento' => $estabelecimento));
                                                    }
                                                }

                                                $this->db->insert('cad_item', $dbdata);
                                                $id_item = $this->db->insert_id();

                                                if (!isset($idx['importado']) && $item_importado_default == true) {
                                                    $this->cad_item_model->define_item_importado($part_number, $estabelecimento, $id_empresa);
                                                } else if (isset($idx['importado']) && $row[$idx['importado']] == strtoupper('SIM')  ) {
                                                    $this->cad_item_model->define_item_importado($part_number, $estabelecimento, $id_empresa);
                                                } else if (isset($idx['importado']) && ($row[$idx['importado']] == strtoupper('NAO') || $row[$idx['importado']] == strtoupper('NÃO'))  ) {
                                                    $this->cad_item_model->remove_item_importado($part_number, $id_empresa, $estabelecimento);
                                                }

                                                $return_nve = $this->cad_item_nve_model->salvar_nve_planilha_homologacao($ncm_recomendada, $row, $idx, $id_item);

                                                if (isset($return_nve->error) && $return_nve->error === TRUE) {
                                                    $log_com_erro['nve_inexistentes'][] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                                                }

                                                $item_novo_inserido = true;

                                                if (!empty($novoPeso)) {
                                                    $motivoItem = "Alteração do Peso: <strong>{$novoPeso}</strong>";    
                                                }

                                                if (!empty($novaLista)) {
                                                    $motivoItem = "Alteração do Peso: <strong>{$novaLista}</strong>";    
                                                }

                                                if (!empty($novaPrioridade) && $old_prioridade != $novaPrioridade) {

                                                    $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
                                                    $motivoItem .= "Alteração de Prioridade: <em>{$old_prioridade}</em> &rarr; <strong>{$novaPrioridade}</strong>". '<br>';
                                                }
                                                
                                                if (!empty($novaMaquina)) {
                                                    $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
                                                    $motivoItem .= "Alteração da Maquina: <strong>{$novaMaquina}</strong>";
                                                }

                                                if (!empty($novaOrigem)) {
                                                    $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
                                                    $motivoItem .= "Alteração da Origem: <strong>{$novaOrigem}</strong>";
                                                }

                                                if (!empty($novasObservacoes)) {
                                                    $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
                                                    $motivoItem .= "Alteração das Observações: <strong>{$novasObservacoes}</strong>";
                                                }

                                                if (!empty($motivoItem)) {
                                                    $this->item_model->update_item($part_number, $id_empresa, array(
                                                        "peso" => $novoPeso,
                                                        "lista_cliente" => $novaLista,
                                                        "prioridade" => $novaPrioridade,
                                                        "maquina" => $novaMaquina,
                                                        "origem" => $novaOrigem,
                                                        "observacoes" => $novasObservacoes
                                                    ), $motivoItem, $estabelecimento);
                                                }

                                                $dbdata2 = array(
                                                    'part_number'       => $part_number,
                                                    'estabelecimento'   => $estabelecimento,
                                                    'tipo_homologacao'  => 'Engenharia',
                                                    'id_usuario'        => sess_user_id(),
                                                    'id_empresa'        => $id_empresa,
                                                    'titulo'            => 'envio',
                                                    'criado_em'         => date("Y-m-d H:i:s"),
                                                    'motivo'            => ''
                                                );

                                                if (isset($idx['id_resp_engenharia']) || isset($idx['id_resp_fiscal'])) {
                                                   
                                                    if (isset($idx['forcar_homologacao'])) {
                                                        $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));
                                                        
                                                        if ($forcar == 'sim')
                                                        {
                                                            $forcar_homologacao = TRUE;
                                                        }
                                                    }
                                                    
                                                    if ($forcar_homologacao)
                                                    {
                                                        $dbdata2['motivo'] = 'Item disponibilizado para Homologação. <br>';
                                                    }

                                                    //Email do usuário responsável - Fiscal
                                                    $email_resp_fiscal = $row[$idx['id_resp_fiscal']];

                                                    if (!empty($email_resp_fiscal)) {
                                                        $dbdata2['motivo'] .= '<strong>Responsável Fiscal: </strong> ' . $email_resp_fiscal . '<br>';
                                                    }

                                                    //Email do usuário responsável - Engenharia
                                                    $email_resp_engenharia = $row[$idx['id_resp_engenharia']];

                                                    if (!empty($email_resp_engenharia)) {
                                                        $dbdata2['motivo'] .= '<strong>Responsável Técnico: </strong>' . $email_resp_engenharia . '<br>';
                                                    }
                                                }

                                                if (isset($dbdata['num_ex_ii']) && !empty($dbdata['num_ex_ii'])) {
                                                    $dbdata2['motivo'] .= '<strong>EX de II: </strong>' . $ex_ii->num_ex . ' - ' . $ex_ii->descricao_linha1 . '<br>';
                                                }

                                                if (isset($dbdata['num_ex_ipi']) && !empty($dbdata['num_ex_ipi'])) {
                                                    $dbdata2['motivo'] .= '<strong>EX de IPI: </strong>' . $ex_ipi->num_ex . ' - ' . $ex_ipi->descricao_linha1 . '<br>';
                                                }

                                                if (isset($dbdata['antidumping']) && !empty($dbdata['antidumping'])) {
                                                    $dbdata2['motivo'] .= '<strong>ANTIDUMPING: </strong>' . $dbdata['antidumping'] . '<br>';
                                                }

                                                if (isset($dbdata['suframa_destaque']) && !empty($dbdata['suframa_destaque'])) {
                                                    $dbdata2['motivo'] .= '<strong>DESTAQUE SUFRAMA: </strong>' . $suframa->DESTAQUE . '<br>';
                                                }
                                                if (isset($dbdata['suframa_ppb']) && !empty($dbdata['suframa_ppb'])) {
                                                    $dbdata2['motivo'] .= '<strong>PPB SUFRAMA: </strong>' . $suframa->PPB . '<br>';
                                                }
                                                if (isset($dbdata['suframa_descricao']) && !empty($dbdata['suframa_descricao'])) {
                                                    $dbdata2['motivo'] .= '<strong>DESCRIÇÃO SUFRAMA: </strong>' . $suframa->DESCRICAO_SUFRAMA . '<br>';
                                                }
                                                if (isset($dbdata['suframa_produto']) && !empty($dbdata['suframa_produto'])) {
                                                    $dbdata2['motivo'] .= '<strong>PRODUTO SUFRAMA: </strong>' . $suframa->PRODUTO . '<br>';
                                                }
                                                if (isset($dbdata['suframa_codigo']) && !empty($dbdata['suframa_codigo'])) {
                                                    $dbdata2['motivo'] .= '<strong>CÓDIGO SUFRAMA: </strong>' . $suframa->CODIGO . '<br>';
                                                }

                                                if (isset($dbdata['li']) && !empty($dbdata['li'])) {
                                                    if ($dbdata['li'] == 'SIM') {
                                                        $dbdata2['motivo'] .= '<strong>LI: </strong>' . $dbdata['li'] . '<br>';
                                                        $dbdata2['motivo'] .= '<strong>DESTAQUE LI: </strong>' . $dbdata['li_destaque'] . '<br>';
                                                        $dbdata2['motivo'] .= '<strong>ORGÃO ANUENTE: </strong>' . $dbdata['li_orgao_anuente'] . '<br>';
                                                    } else {
                                                        $dbdata2['motivo'] .= '<strong>LI: </strong>' . $dbdata['li'] . '<br>';
                                                    }
                                                }

                                                if (isset($return_nve->logs) && !empty($return_nve->logs)) {
                                                    foreach ($return_nve->logs as $log) {
                                                        $dbdata2['motivo'] .= "<strong>NVE [{$log['nve_atributo']}]: </strong>" . $log['nve_valor'] . '<br>';
                                                    }
                                                }

                                                $this->item_log_model->save($dbdata2);

                                                $log_inseridos[] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);

                                                $forcar_mudanca = FALSE;
                                                if (isset($idx['forcar_homologacao'])) {
                                                    $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));
                                                    
                                                    if ($forcar == 'sim')
                                                    {
                                                        $forcar_mudanca = TRUE;
                                                    }
                                                }

                                                $this->atualizar_status($item_base, $part_number, $estabelecimento, $id_empresa, $forcar_mudanca);
                                            
                                                if (!empty($dbdata['ncm_proposto']) && !empty($id_item))
                                                {
                                                    $this->load->model('cad_item_model');
                                                    $this->cad_item_model->limpar_atributos_nao_relacionados($id_item, $dbdata['ncm_proposto'],  true);

                                                    $query =  $this->db->query("SELECT 
                                                        ci.part_number,ci.estabelecimento,ci.id_empresa,
                                                            CASE 
                                                                WHEN COUNT(*) = 0 
                                                                OR COUNT(CASE WHEN attr.codigo IS NULL OR attr.codigo = '' THEN 1 END) = COUNT(*) THEN '1'
                                                
                                                                WHEN COUNT(CASE WHEN attr.obrigatorio = 1 AND (attr.codigo IS NULL OR attr.codigo = '') THEN 1 END) > 0 THEN '2'
                                                
                                                                WHEN COUNT(CASE 
                                                                    WHEN (attr.obrigatorio = 0 AND (attr.codigo IS NULL OR attr.codigo = '')) 
                                                                            OR (attr.id_item IS NULL) THEN 1 
                                                                    ELSE NULL 
                                                                    END) > 0 THEN '3'
                                        
                                                                WHEN COUNT(CASE WHEN attr.codigo IS NOT NULL AND attr.codigo <> '' THEN 1 END) = COUNT(*) THEN '4'
                                                
                                                                ELSE '0'
                                                            END AS status_preenchimento
                                                        FROM cad_item ci
                                                        LEFT JOIN cad_item_attr attr ON ci.id_item = attr.id_item
                                                        WHERE ci.id_item = '{$id_item}'
                                                        GROUP BY ci.id_item
                                                    ");
                                                    
                                                    while ($item = $query->unbuffered_row()) 
                                                    {
                                                        $this->db->query(" UPDATE item i
                                                        SET i.status_attr = '{$item->status_preenchimento}'
                                                            WHERE i.part_number = '{$item->part_number}' 
                                                            AND i.estabelecimento = '{$item->estabelecimento}'
                                                            AND i.id_empresa = '{$item->id_empresa}'
                                                            AND i.status_attr <> '{$item->status_preenchimento}'");
                                                    }
                                                                                              
                                                }
                                            
                                            }

                                            /* Aqui será realizado o último check
                                             * entre item e cad_item, validando os campos chaves
                                             * para que sejam feitas as finalizações de
                                             * inclusão/atualização conforme a planilha.
                                            */
                                            $this->db->select('ci.id_item, i.*');

                                            $where = array(
                                                'i.part_number'     => $dbdata['part_number'],
                                                'i.estabelecimento' => $estabelecimento,
                                                'i.id_empresa'      => $id_empresa
                                            );

                                            $this->db->where($where);
                                            $this->db->join(
                                                'cad_item ci',
                                                'ci.part_number = i.part_number and ci.estabelecimento = i.estabelecimento and ci.id_empresa = i.id_empresa',
                                                'inner',
                                                false
                                            );

                                            $query = $this->db->get('item i');

                                            if ($query->num_rows() > 0) {
                                                $dbdata = array();

                                                $irow = $query->row();

                                                if ((isset($item_novo_inserido)) && $item_novo_inserido === true) {
                                                    $dbdata['data_envio'] = date('Y-m-d H:i:s');
                                                }

                                                /**
                                                 * #4334 - IMPORTAÇÃO PLANILHA DE HOMOLOGAÇÃO COM OPÇÃO VALIDAÇÕES DESMARCADA
                                                 *  Adicionado tratativa para inserir a proposta completa na tabela item conforme solicitado.
                                                 */
                                                if (isset($idx['descricao_proposta_completa'])) {
                                                    $nova_descricao_completa = formatar_texto($can_formatar_texto, trim($row[$idx['descricao_proposta_completa']]));
                                                    if (
                                                        $validar !== TRUE
                                                        && $irow->descricao_proposta_completa != $nova_descricao_completa
                                                    ) {
                                                        $dbupdate_item = array();
                                                        $dbupdate_item['descricao_proposta_completa'] = $nova_descricao_completa;
                                                        $dbupdate_item['houve_descricao_completa_manual'] = 0;

                                                        $descricao_completa_texto = $irow->descricao_proposta_completa ? $irow->descricao_proposta_completa : '<em>Não informado</em>';
                                                        $nova_descricao_completa_texto = $nova_descricao_completa ? $nova_descricao_completa : '<em>Não informado</em>';

                                                        $motivo_item = "Alteração da Descrição proposta completa: " . $descricao_completa_texto . " -> <strong>" . $nova_descricao_completa_texto . "</strong><br />";

                                                        $this->load->model('item_model');
                                                        if ($force == "sim") {
                                                            $dbdata_item_ret = $this->item_model->update_item($irow->part_number, $irow->id_empresa, $dbupdate_item, $motivo_item, $irow->estabelecimento);
                                                        }
                                                        $dbdata['descricao_proposta_completa'] = $nova_descricao_completa;
                                                    }
                                                }
                                                if (isset($idx['inf_adicionais'])) {
                                                    $inf_adicionais = formatar_texto($can_formatar_texto, trim($row[$idx['inf_adicionais']]));
                                                    if (!empty($inf_adicionais) && isset($irow->inf_adicionais) && $inf_adicionais != $irow->inf_adicionais)
                                                        $dbdata['inf_adicionais'] = $inf_adicionais;
                                                }

                                                if (isset($idx['evento'])) {
                                                    $evento = formatar_texto($can_formatar_texto, trim($row[$idx['evento']]));

                                                    /* Novas regras para o campo 'Evento'
                                                     * Chamado: #2027
                                                     *
                                                     * Caso o campo evento na tabela item não esteja vazio
                                                     * e as validações especiais estejam ativas
                                                    */
                                                    if ($validar === TRUE && !empty($irow->evento)) {
                                                        /* Caso o campo evento da planilha foi informado e
                                                         * é diferente do encontrado na tabela item...
                                                        */
                                                        if (!empty($evento) && ($irow->evento != $evento)) {
                                                            $validar_evento[] = array(
                                                                'item'        => $irow,
                                                                'novo_evento' => $evento
                                                            );
                                                        }
                                                    } else {
                                                        // Caso o campo da tabela item esteja vazio
                                                        if (empty($evento)) {
                                                            /* E o campo na planilha não foi informado
                                                             * será utilizado um evento com nome genérico com
                                                             * base na data atual.
                                                            */
                                                            $current_date = date('Y-m-d');
                                                            $evento = 'PACOTE ' . $current_date;
                                                        }

                                                        /* Adiciona no grupo para o update da tabela item conforme o evento
                                                         * da planilha ou com base na data, caso não esteja nulo na planilha
                                                        */
                                                        $dbdata['evento'] = $evento;
                                                    }
                                                }
                                                $force = (isset($idx['forcar_atualizacao']) ? mb_strtolower(trim($row[$idx['forcar_atualizacao']])) : 'nao');

                                                if ($force == "sim") {
                                                    /* Atualiza o item */
                                                    if (count($dbdata) > 0) {
                                                        $this->db->update('item i', $dbdata, $where);
                                                    }
                                                }
                                            }
                                        } catch (Exception $e) {
                                            $log_com_erro['excecao_erro'][] = array('part_number' => $part_number, 'message' => $e->getMessage());
                                        }
                                    }
                                }

                                break;
                            }
                        }

                        $reader->close();

                        $msg = '';

                        if (!empty($log_com_erro)) {
                            $type = 'error';

                            $msg .= '<h4>Erro</h4>';
                            $msg .= 'Arquivo XLSX <b>' . $upload_data['orig_name'] . '</b> recebido, mas ocorreram alguns erros.';
                        } else {
                            $type = 'success';

                            $msg .= '<h4>Sucesso</h4>';
                            $msg .= 'Arquivo XLSX <b>' . $upload_data['orig_name'] . '</b> recebido e processado com sucesso!';
                        }

                        $msg .= '<div id="log-accordion">';
                        $msg .= '<ul>';

                        // Itens inseridos
                        $msg .= '<li>';
                        if (!empty($log_inseridos)) {
                            $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#inseridos">';
                            $msg .= 'Itens inseridos: <b>' . count($log_inseridos) . '</b></a>';
                            $msg .= '<div id="inseridos" class="collapse">';
                            $msg .= format_log_as_html($log_inseridos);
                            $msg .= '</div>';
                        } else {
                            $msg .= 'Itens inseridos: <b>0</b>';
                        }
                        $msg .= '</li>';

                        // Itens atualizados
                        $msg .= '<li>';
                        if (!empty($log_atualizados)) {
                            $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#atualizados">';
                            $msg .= 'Itens atualizados: <b>' . count($log_atualizados) . '</b></a>';
                            $msg .= '<div id="atualizados" class="collapse">';
                            $msg .= format_log_as_html($log_atualizados);
                            $msg .= '</div>';
                        } else {
                            $msg .= 'Itens atualizados: <b>0</b>';
                        }
                        $msg .= '</li>';

                        // Itens com erro
                        $msg .= '<li>';
                        if (!empty($log_com_erro)) {
                            $total_itens_erro = 0;
                            $html_li_item = '';

                            foreach ($log_com_erro as $log_key => $log_val) {
                                foreach ($log_val as $log) {
                                    $total_itens_erro++;

                                    if (!empty($log['part_number']) && !empty($log['estabelecimento'])) {
                                        $html_li_item .= '<li>Part number [<strong>' . $log['part_number'] . '</strong>][<b>' . $log['estabelecimento'] . '</b>]: ';
                                    } else if (!empty($log['part_number'])) {
                                        $html_li_item .= '<li>Part number [<strong>' . $log['part_number'] . '</strong>]: ';
                                    } else {
                                        $html_li_item .= '<li>Linha [<strong>' . $log['linha'] . '</strong>]: ';
                                    }

                                    switch ($log_key) {
                                        case 'descricao_maximo_de_caracteres':
                                            $html_li_item .= sprintf($error_log_lang[$log_key], $empresa->descricao_max_caracteres) . '</li>';
                                            break;

                                        default:
                                            $html_li_item .= $error_log_lang[$log_key] . '</li>';
                                            break;
                                    }
                                }
                            }

                            $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#com-erro">';
                            $msg .= 'Itens com erro: <b>' . $total_itens_erro . '</b></a>';
                            $msg .= '<div id="com-erro" class="collapse"><ul>';
                            $msg .= $html_li_item;
                            $msg .= '</ul></div>';
                        } else {
                            $msg .= 'Itens com erro: <b>0</b>';
                        }
                        $msg .= '</li>';

                        $msg .= '</ul>';
                        $msg .= '</div>';

                        $message_on_render = '';
                        $message_on_render .= $this->message_config($msg, $type);

                        $buttons = '';

                        if ((isset($validar_descricao_item)) && count($validar_descricao_item) > 0) {
                            $this->session->set_userdata('validar-dados-itens', $validar_descricao_item);
                            $buttons .= '<a href="' . base_url('uploadnext/validar_descricao_item') . '" class="btn btn-primary" data-toggle="modal" data-target="#validar-dados-itens-modal" style="margin-right: 15px; margin-top: 10px;">Aprovar alterações de descrição</a>';
                        }

                        if ((isset($validar_grupo_tarifario) && count($validar_grupo_tarifario) > 0)) {
                            $this->session->set_userdata('validar-grupo-tarifario-itens', $validar_grupo_tarifario);
                            $message_on_render .= $this->message_config('<h4>Validação de alteração de Grupo Tarifário</h4>Alguns itens já aprovados possuem alterações de Grupo Tarifário.<br /><a href="' . base_url('uploadnext/validar_grupo_tarifario_itens') . '" data-toggle="modal" data-target="#validar-grupo-tarifario-itens-modal">Clique aqui para ver a lista de itens</a>');
                        }

                        if (!empty($validar_funcao_aplicacao_marca)) {
                            $this->session->set_userdata('validar-funcao-aplicacao-marca', $validar_funcao_aplicacao_marca);
                            $buttons .= '<a href="' . base_url('uploadnext/validar_funcao_aplicacao_marca') . '" class="btn btn-primary" data-toggle="modal" data-target="#validar-funcao-aplicacao-marca-modal" style="margin-right: 15px; margin-top: 10px;">Aprovar alterações de campos adicionais</a>';
                        }

                        if (isset($validar_evento)) {
                            $this->session->set_userdata('validar-evento', $validar_evento);
                            $buttons .= '<a href="' . base_url('uploadnext/validar_evento') . '" class="btn btn-primary" data-toggle="modal" data-target="#validar-evento" style="margin-top: 10px;">Aprovar alterações de Evento</a>';
                        }

                        if (!empty($buttons)) {
                            $message_on_render .= $this->message_config('<h4>Validação de alteração de dados</h4> Alguns itens precisam de aprovação pois possuem dados divergentes da planilha e/ou definições do Grupo Tarifário.<br />' . $buttons);
                        }

                        if ((isset($log_com_erro)) && count($log_com_erro) > 0) {
                            $log_final = "";
                            foreach ($log_com_erro as $log_key => $log_val) {
                                $log_final .= '<li><strong>' . count($log_val) . ' item(ns)</strong>: ';

                                switch ($log_key) {
                                    case 'descricao_maximo_de_caracteres':
                                        $log_final .= sprintf($error_log_lang[$log_key], $empresa->descricao_max_caracteres) . '</li>';
                                        break;

                                    default:
                                        $log_final .= $error_log_lang[$log_key] . '</li>';
                                        break;
                                }
                            }

                            if (!empty($log_final)) {
                                $message_on_render .= $this->message_config("<h4>Atenção: </h4><ul>{$log_final}</ul>", "warning");
                            }
                        }

                        $this->message_next_render($message_on_render, NULL, TRUE);
                    }

                    unlink($upload_data['full_path']);
			        $fim = microtime(true);
                    $msg = "Data: ".date('d/m/Y \à\s H:i:s')." uploadnext - Concluído - Tempo de Processamento: ".number_format(($fim - $inicio),2)." Linhas Processadas: ".$count_register." Empresa: ".sess_user_company()." Usuario: ".sess_user_id();
                    $this->functionShutdown($msg);
                    redirect('/uploadnext/');
                }
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Enviar itens para homologação', '/uploadnext/');

        $this->render('uploadnext', $data);
    }

    public function atualizar_status($item_base, $part_number, $estabelecimento, $id_empresa, $forcar_mudanca = FALSE, $tipo = 'novos', $id_item = null, $homologar = FALSE, $novo_grupo_tarifario = FALSE )
    {
        if ($forcar_mudanca == FALSE && $tipo != 'novos')
        {
            // Não deve haver troca de status de itens já homologados ou reprovados
            if (($item_base->id_status == '2' || $item_base->id_status == '3') && $novo_grupo_tarifario == FALSE  )
            {
                return;
            }

            if ($homologar == FALSE)
            {
                return;
            }
        }

        $this->load->library("Item/Status");

        $this->status->set_status("homologar");
        $this->status->update_item($part_number, $estabelecimento, $id_empresa);

        if ($tipo != 'novos' && !empty($id_item))
        {
            $this->cad_item_homologacao_model->drop_item($id_item);
        }
    }

    public function validar_descricao_item()
    {
        $data = array();
        $can_formatar_texto = company_can("formatar_texto");

        if ($this->input->post('item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_log_model');
            $errors = $success = array();
            $itens                          = $this->input->post('item');
            $itens_descricao                = $this->input->post('item_descricao');

            foreach ($itens as $k => $id_item) {
                $item = $this->cad_item_model->get_entry($id_item);
                $nova_descricao_sugerida = formatar_texto($can_formatar_texto, $itens_descricao[$k]);

                $dbupdate = array(
                    'descricao_mercado_local'   => $nova_descricao_sugerida,
                    'houve_descricao_manual'    => 0
                );

                $motivo = "";

                if ($nova_descricao_sugerida != $item->descricao_mercado_local) {
                    $motivo = "Alteração da descrição proposta resumida: " . $item->descricao_mercado_local . " -> <strong>" . $nova_descricao_sugerida . "</strong><br />";
                }

                if (!empty($motivo)) {
                    if ($this->cad_item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $motivo, $item->estabelecimento)) {
                        $success[] = $item->part_number;
                    } else {
                        $errors[] = $item->part_number;
                    }
                }
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
                echo '<div class="alert alert-danger">' . $error_message . '</div>';
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
                echo '<div class="alert alert-success">' . $success_message . '</div>';
            }

            return TRUE;
        }

        $list_arr = $this->session->userdata('validar-dados-itens');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;

        $this->load->view('uploadnext-validar-dados-itens-modal', $data);
    }

    public function validar_evento()
    {
        $data = array();
        if ($this->input->post('id_item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_model');
            $this->load->model('item_log_model');
            $errors = $success = array();
            $itens        = $this->input->post('id_item');
            $itens_evento = $this->input->post('item_evento');

            foreach ($itens as $k => $id_item) {
                $cad_item = $this->cad_item_model->get_entry($id_item);
                $item = $this->item_model->get_entry($cad_item->part_number, $cad_item->id_empresa, $cad_item->estabelecimento);

                $novo_evento = $itens_evento[$id_item];

                $dbupdate = array(
                    'evento'   => $novo_evento,
                );

                $motivo = "";

                if ($novo_evento != $item->evento) {
                    $motivo = "Alteração de evento: " . $item->evento . " -> <strong>" . $novo_evento . "</strong><br />";
                }

                if (!empty($motivo)) {
                    if ($this->item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $motivo, $item->estabelecimento)) {
                        $success[] = $item->part_number;
                    } else {
                        $errors[] = $item->part_number;
                    }
                }
            }

            if (count($errors) == 0 && count($success) == 0) {
                $info_message = '<strong>Hmmm..</strong> Parece que os itens selecionados já estão atualizados.';
                echo '<div class="alert alert-info">' . $info_message . '</div>';

                return true;
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
                echo '<div class="alert alert-danger">' . $error_message . '</div>';

                return true;
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
                echo '<div class="alert alert-success">' . $success_message . '</div>';

                return true;
            }

            return false;
        }

        $list_arr = $this->session->userdata('validar-evento');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;

        $this->load->view('uploadnext-validar-evento', $data);
    }

    public function validar_funcao_aplicacao_marca()
    {
        $data = array();

        if ($this->input->post('item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_log_model');
            $this->load->model('empresa_model');

            $errors = $success = array();

            $itens = $this->input->post('item');

            $id_empresa = $this->input->post('id_empresa');
            $empresa = $this->empresa_model->get_entry($id_empresa);

            $campos_adicionais = explode('|', $empresa->campos_adicionais);

            $has_funcao = FALSE;
            $has_inf_adicionais = FALSE;
            $has_aplicacao = FALSE;
            $has_marca = FALSE;
            $has_material_constitutivo = FALSE;
            $has_descricao_proposta_completa = FALSE;

            if (in_array('funcao', $campos_adicionais)) {
                $has_funcao = TRUE;
                $itens_funcao = $this->input->post('item_funcao');
            }

            if (in_array('inf_adicionais', $campos_adicionais)) {
                $has_inf_adicionais = TRUE;
                $itens_inf_adicionais = $this->input->post('item_inf_adicionais');
            }

            if (in_array('aplicacao', $campos_adicionais)) {
                $has_aplicacao = TRUE;
                $itens_aplicacao = $this->input->post('item_aplicacao');
            }

            if (in_array('marca', $campos_adicionais)) {
                $has_marca = TRUE;
                $itens_marca = $this->input->post('item_marca');
            }

            if (in_array('material_constitutivo', $campos_adicionais)) {
                $has_material_constitutivo = TRUE;
                $itens_material_constitutivo = $this->input->post('item_material_constitutivo');
            }

            if (in_array('descricao_proposta_completa', $campos_adicionais)) {
                $has_descricao_proposta_completa = TRUE;
                $itens_descricao_completa = $this->input->post('item_descricao_completa');
            }

            foreach ($itens as $k => $id_item) {
                $item = $this->cad_item_model->get_entry($id_item);

                $motivo = "";
                $dbupdate = array();

                if ($has_funcao === TRUE && isset($itens_funcao[$k])) {
                    $nova_funcao = $itens_funcao[$k];

                    if ($nova_funcao != $item->funcao) {
                        $dbupdate['funcao'] = $nova_funcao;
                        $dbupdate['houve_funcao_manual'] = 0;

                        $funcao_texto = $item->funcao ? $item->funcao : '<em>Não informado</em>';
                        $nova_funcao_texto = $nova_funcao ? $nova_funcao : '<em>Não informado</em>';

                        $motivo .= "Alteração da função: " . $funcao_texto . " -> <strong>" . $nova_funcao_texto . "</strong><br />";
                    }
                }

                if ($has_inf_adicionais === TRUE && isset($itens_inf_adicionais[$k])) {
                    $nova_inf_adicionais = $itens_inf_adicionais[$k];

                    if ($nova_inf_adicionais != $item->inf_adicionais) {
                        $dbupdate['inf_adicionais'] = $nova_inf_adicionais;
                        $dbupdate['houve_inf_adicionais_mnual'] = 0;

                        $inf_adicionais_texto = $item->inf_adicionais ? $item->inf_adicionais : '<em>Não informado</em>';
                        $nova_inf_adicionais_texto = $nova_inf_adicionais ? $nova_inf_adicionais : '<em>Não informado</em>';

                        $motivo .= "Alteração das Informações Adicionais: " . $inf_adicionais_texto . " -> <strong>" . $nova_inf_adicionais_texto . "</strong><br/>";
                    }
                }

                if ($has_aplicacao === TRUE && isset($itens_aplicacao[$k])) {
                    $nova_aplicacao = $itens_aplicacao[$k];

                    if ($nova_aplicacao != $item->aplicacao) {
                        $dbupdate['aplicacao'] = $nova_aplicacao;
                        $dbupdate['houve_aplicacao_manual'] = 0;

                        $aplicacao_texto = $item->aplicacao ? $item->aplicacao : '<em>Não informado</em>';
                        $nova_aplicacao_texto = $nova_aplicacao ? $nova_aplicacao : '<em>Não informado</em>';

                        $motivo .= "Alteração da aplicação: " . $aplicacao_texto . " -> <strong>" . $nova_aplicacao_texto . "</strong><br />";
                    }
                }

                if ($has_marca === TRUE && isset($itens_marca[$k])) {
                    $nova_marca = $itens_marca[$k];

                    if ($nova_marca != $item->marca) {
                        $dbupdate['marca'] = $nova_marca;
                        $dbupdate['houve_marca_manual'] = 0;

                        $marca_texto = $item->marca ? $item->marca : '<em>Não informado</em>';
                        $nova_marca_texto = $nova_marca ? $nova_marca : '<em>Não informado</em>';

                        $motivo .= "Alteração da marca: " . $marca_texto . " -> <strong>" . $nova_marca_texto . "</strong><br />";
                    }
                }

                if ($has_material_constitutivo === TRUE && isset($itens_material_constitutivo[$k])) {
                    $novo_material_constitutivo = $itens_material_constitutivo[$k];

                    if ($novo_material_constitutivo != $item->material_constitutivo) {
                        $dbupdate['material_constitutivo'] = $novo_material_constitutivo;
                        $dbupdate['houve_material_constitutivo_manual'] = 0;

                        $material_constitutivo_texto = $item->material_constitutivo ? $item->material_constitutivo : '<em>Não informado</em>';
                        $novo_material_constitutivo_texto = $novo_material_constitutivo ? $novo_material_constitutivo : '<em>Não informado</em>';

                        $motivo .= "Alteração do material constitutivo: " . $material_constitutivo_texto . " -> <strong>" . $novo_material_constitutivo_texto . "</strong><br />";
                    }
                }

                if ($has_descricao_proposta_completa === TRUE && isset($itens_descricao_completa[$k])) {
                    $nova_descricao_completa = $itens_descricao_completa[$k];

                    if ($nova_descricao_completa != $item->descricao_proposta_completa) {
                        $dbupdate_item['descricao_proposta_completa'] = $nova_descricao_completa;
                        $dbupdate_item['houve_descricao_completa_manual'] = 0;

                        $descricao_completa_texto = $item->descricao_proposta_completa ? $item->descricao_proposta_completa : '<em>Não informado</em>';
                        $nova_descricao_completa_texto = $nova_descricao_completa ? $nova_descricao_completa : '<em>Não informado</em>';

                        $motivo_item = "Alteração da Descrição proposta completa: " . $descricao_completa_texto . " -> <strong>" . $nova_descricao_completa_texto . "</strong><br />";

                        $this->load->model('item_model');
                        $dbdata_item_ret = $this->item_model->update_item($item->part_number, $item->id_empresa, $dbupdate_item, $motivo_item, $item->estabelecimento);
                    }
                }

                if (!empty($motivo)) {
                    if (!empty($dbupdate)) {
                        $dbupdate_ret = $this->cad_item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $motivo, $item->estabelecimento);
                    }
                }

                if (isset($dbdata_item_ret) || isset($dbupdate_ret)) {
                    $success[] = $item->part_number;
                } else {
                    $errors[] = $item->part_number;
                }
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
                echo '<div class="alert alert-danger">' . $error_message . '</div>';
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
                echo '<div class="alert alert-success">' . $success_message . '</div>';
            }

            return TRUE;
        }

        $list_arr = $this->session->userdata('validar-funcao-aplicacao-marca');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;

        $this->load->view('uploadnext-validar-funcao-aplicacao-marca-modal', $data);
    }

    public function validar_dados_itens()
    {
        $data = array();
        $can_formatar_texto = company_can("formatar_texto");

        if ($this->input->post('item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_log_model');
            $errors = $success = array();
            $itens                          = $this->input->post('item');
            $itens_descricao                = $this->input->post('item_descricao');
            $itens_subsidio                 = $this->input->post('item_subsidio');
            $itens_caracteristicas          = $this->input->post('item_caracteristicas');
            $itens_memoria_classificacao    = $this->input->post('item_memoria_classificacao');

            foreach ($itens as $k => $id_item) {
                $item = $this->cad_item_model->get_entry($id_item);
                $nova_descricao_sugerida =  formatar_texto($can_formatar_texto, $itens_descricao[$k]);
                $novo_subsidio = $itens_subsidio[$k];
                $nova_caracteristicas = $itens_caracteristicas[$k];
                $nova_memoria_classificacao = $itens_memoria_classificacao[$k];

                $dbupdate = array(
                    'descricao_mercado_local'   => $nova_descricao_sugerida,
                    'houve_descricao_manual'    => 0
                );

                if ($nova_descricao_sugerida !== $item->descricao_mercado_local) {
                    $motivo = "Descrição: " . $item->descricao_mercado_local . " -> " . $nova_descricao_sugerida . "<br />";
                }

                if ($novo_subsidio !== $item->subsidio) {
                    $dbupdate['subsidio'] = $novo_subsidio;
                    $motivo .= "Subsídio: " . $item->subsidio . " -> " . $novo_subsidio . "<br />";
                }

                if ($nova_caracteristicas !== $item->caracteristicas) {
                    $dbupdate['caracteristicas'] = $nova_caracteristicas;
                    $motivo .= "Características: " . $item->caracteristicas . " -> " . $nova_caracteristicas . "<br />";
                }

                if ($nova_memoria_classificacao !== $item->memoria_classificacao) {
                    $dbupdate['memoria_classificacao'] = $nova_memoria_classificacao;
                    $motivo .= "Memória de Classificação: " . $item->memoria_classificacao . " -> " . $nova_memoria_classificacao;
                }

                if ($this->cad_item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $motivo, $item->estabelecimento)) {
                    $success[] = $item->part_number;
                } else {
                    $errors[] = $item->part_number;
                }
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
                echo '<div class="alert alert-danger">' . $error_message . '</div>';
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
                echo '<div class="alert alert-success">' . $success_message . '</div>';
            }

            return TRUE;
        }

        $list_arr = $this->session->userdata('validar-dados-itens');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;

        $this->load->view('uploadnext-validar-dados-itens-modal', $data);
    }

    public function validar_grupo_tarifario_itens()
    {
        $data = array();

        if ($this->input->post('item')) {
            $this->load->model('cad_item_model');
            $this->load->model('item_log_model');

            $errors = $success = array();

            $itens = $this->input->post('item');
            $itens_novo_grupo_tarifario  = $this->input->post('item_novo_grupo_tarifario');

            foreach ($itens as $k => $id_item) {
                $item = $this->cad_item_model->get_entry($id_item);
                $id_grupo_tarifario = $itens_novo_grupo_tarifario[$k];
                $dbupdate = array(
                    'id_grupo_tarifario' => $id_grupo_tarifario
                );

                if ($this->cad_item_model->update_item($item->part_number, $item->id_empresa, $dbupdate, $item->estabelecimento)) {
                    $success[] = $item->part_number;
                } else {
                    $errors[] = $item->part_number;
                }
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> Os seguintes foram atualizados com sucesso: ' . implode(', ', $success) . '';
            }

            echo '<div class="alert alert-success">' . $success_message . '</div>';

            return TRUE;
        }

        $list_arr = $this->session->userdata('validar-grupo-tarifario-itens');

        if (empty($list_arr) || count($list_arr) == 0) {
            show_404();
        }

        $data['list'] = $list_arr;
        $this->load->view('uploadnext-validar-grupo-tarifario-itens-modal', $data);
    }
}
