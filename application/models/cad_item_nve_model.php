<?php

class Cad_item_nve_model extends MY_Model
{

    public $_table = 'cad_item_nve';

    public function __construct()
    {
        parent::__construct();
    }

    public function get_entry($id_item)
    {
        $this->db->where('id_item', $id_item);
        $query = $this->db->get($this->_table);

        return $query->row();
    }

    public function get_item_atributo($id_item, $nve_atributo)
    {
        $this->db->where('id_item', $id_item);
        $this->db->where('nve_atributo', $nve_atributo);

        $query = $this->db->get($this->_table);

        return $query->row();
    }

    public function check_item_atributo($id_item, $nve_atributo, $nve_valor)
    {
        $this->db->where('id_item', $id_item);
        $this->db->where('nve_atributo', $nve_atributo);
        $this->db->where('nve_valor', $nve_valor);

        $query = $this->db->get($this->_table);

        return $query->row();
    }

    public function get_entries($id_item)
    {
        $this->db->select('nve_atributo, nm_atributo, nm_especif_ncm, nve_valor');

        $this->db->where('cnve.id_item', $id_item);

        $this->db->join('cad_item c', 'c.id_item=cnve.id_item', 'inner');
        $this->db->join('nve_atributo_valor nvev', 'nvev.cd_atributo_ncm=cnve.nve_atributo AND nvev.cd_especif_ncm=cnve.nve_valor AND nvev.cd_nomenc_ncm=c.ncm_proposto', 'inner');
        $this->db->join('nve_atributo nve', 'nve.cd_atributo_ncm=nvev.cd_atributo_ncm AND nve.cd_nomenc_ncm=c.ncm_proposto', 'inner');

        $this->db->order_by('nve_atributo', 'ASC');
        $query = $this->db->get($this->_table . ' cnve');
        return $query->result();
    }

    public function has_nve($id_item)
    {
        $this->db->where('id_item', $id_item);
        $query = $this->db->get($this->_table);

        if ($query->num_rows() > 0) {
            return TRUE;
        } else {
            return FALSE;
        }
    }

    public function drop_itens($id_item)
    {
        $this->db->where('id_item', $id_item);
        return $this->db->delete($this->_table);
    }

    public function save($data, $where = null)
    {
        $this->db->set('nve_atributo', $data['nve_atributo']);
        $this->db->set('nve_valor', $data['nve_valor']);
        $this->db->set('id_item', $data['id_item'], FALSE);

        return $this->db->replace($this->_table);
    }

    public function salvar_nve_planilha_homologacao($ncm, $row, $idx, $id_item = NULL)
    {
        $attrs = array(
            'AA', 'AB', 'AC', 'AD',
            'AE', 'AF', 'AG', 'AH'
        );

        $error = false;
        $logs = array();

        if (!empty($ncm)) {
            if (!empty($id_item))
                $this->drop_itens($id_item);

            foreach ($attrs as $attr) {
                $key_atributo = "nve_atributo_" . strtolower($attr);
                $key_valor = "nve_valor_" . strtolower($attr);
                if (isset($idx[$key_atributo]) && isset($idx[$key_valor])) {
                    if (isset($row[$idx[$key_atributo]]) && isset($row[$idx[$key_valor]])) {

                        $atributo = $row[$idx[$key_atributo]];
                        $valor = $row[$idx[$key_valor]];
                        if (!empty($atributo) && !empty($valor) && !empty($id_item)) {
                            $nve = $this->nve_atributo_model->get_valor($ncm, $atributo, $valor);
                            if ($nve && !empty($nve) && !empty($nve->cd_atributo_ncm) && !empty($nve->cd_especif_ncm)) {
                                $this->save(array(
                                    'nve_atributo' => $nve->cd_atributo_ncm,
                                    'nve_valor' => $nve->cd_especif_ncm,
                                    'id_item' => $id_item
                                ));

                                $logs[] = array(
                                    'nve_atributo' => $nve->cd_atributo_ncm,
                                    'nve_valor' => $nve->cd_especif_ncm
                                );
                            } else {
                                $error = true;
                            }
                        }
                    }
                }
            }
        }

        return (object) array(
            'error' => $error,
            'logs' => $logs
        );
    }
}
