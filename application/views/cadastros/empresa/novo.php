<?php echo form_open_multipart('', array('class' => 'form-horizontal')) ?>

<div class="row">

    <ul class="nav nav-tabs" role="tablist">
        <li class="active" role="presentation">
            <a href="#dados" aria-controls="home" role="tab" data-toggle="tab">Dados básicos</a>
        </li>
        <li role="presentation">
            <a href="#configuracoes" aria-controls="home" role="tab" data-toggle="tab">Configurações</a>
        </li>
        <li role="presentation">
            <a href="#configuracoes_financeiras" aria-controls="home" role="tab" data-toggle="tab">Configurações Financeiras</a>
        </li>
        <li role="presentation">
            <a href="#curva" aria-controls="home" role="tab" data-toggle="tab">Curva ABC</a>
        </li>
        <li role="presentation">
            <a href="#usuario-api" aria-controls="home" role="tab" data-toggle="tab">Usuários API</a>
        </li>
    </ul>

    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="dados">

            <div class="col-md-12">

                <legend>
                    <h2>Nova empresa</h2>
                </legend>

                <div class="form-group">
                    <label for="input-razao_social" class="col-sm-2 control-label">Razão Social</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="razao_social" id="input-razao_social" value="<?php echo set_value('razao_social') ?>" placeholder="Razão Social">
                    </div>
                </div>

                <div class="form-group">
                    <label for="input-nome_fantasia" class="col-sm-2 control-label">Nome Fantasia</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="nome_fantasia" id="input-nome_fantasia" placeholder="Nome Fantasia" value="<?php echo set_value('nome_fantasia') ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="input-cnpj" class="col-sm-2 control-label">CNPJ</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="cnpj" id="input-cnpj" placeholder="CNPJ" value="<?php echo set_value('cnpj') ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="select-segmento" class="col-sm-2 control-label">Segmento</label>
                    <div class="col-sm-10">
                        <select class="form-control custom" name="id_segmento" id="select-segmento">
                            <option value="">[Selecione]</option>
                            <?php foreach ($segmentos as $segmento) { ?>
                                <option <?php echo set_select('id_segmento', $segmento->id_segmento); ?> value="<?php echo $segmento->id_segmento ?>"><?php echo $segmento->descricao ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="select-segmento" class="col-sm-2 control-label">Empresa Ativa</label>
                    <div class="col-sm-10">
                        <select class="form-control" name="ativo" id="ativo">
                            <option <?php echo set_select('ativo', '1') ?> value="1">Ativa</option>
                            <option <?php echo set_select('ativo', '0') ?> value="0">Inativa</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="select-paises" class="col-sm-2 control-label">Multi Paises</label>
                    <div class="col-sm-10">
                        <select class="form-control selectpicker" multiple title="Paises onde atua" data-count-selected-text="{0} paises selecionados" data-selected-text-format="count" name="paises[]" id="paies" data-width="100%">
                            <option value="">[Selecione]</option>
                            <?php foreach ($paises as $pais) { ?>
                                <option <?php echo set_select('id_pais', $pais->id_pais); ?> value="<?php echo $pais->id_pais ?>"><?php echo $pais->nome ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="select-id_gerente_de_projeto" class="col-sm-2 control-label">Squad Responsável</label>
                    <div class="col-sm-10">
                        <select class="form-control selectpicker" name="id_squad" id="id_squad" data-show-subtext="true" title="Selecione o squad responsável" data-live-search="true">
                            <?php foreach ($squads as $squad) { ?>
                                <option value="<?php echo $squad->id_squad; ?>" data-subtext="<?php echo $squad->slug; ?>"><?php echo $squad->nome; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="select-perfil" class="col-sm-2 control-label">Perfil padrão para os usuários</label>
                    <div class="col-sm-10">
                        <select class="form-control custom" name="perfil_usuario_padrao_id" id="select-perfil">
                            <option value="">[Selecione]</option>
                            <?php foreach ($perfis as $perfil) { ?>
                                <option <?php echo set_select('perfil_usuario_padrao_id', $perfil->id_perfil); ?>
                                    value="<?php echo $perfil->id_perfil ?>">
                                    <?php echo $perfil->descricao ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>

                <div class="clearfix"></div>
                <div class="form-group">
                    <label for="arquivo" class="col-sm-2 control-label">Upload da logo</label>
                    <div class="col-sm-10">

                        <div class="fileupload fileupload-new" data-provides="fileupload">
                            <span class="btn btn-primary btn-success btn-file"><span class="fileupload-new">Selecione um arquivo</span>
                                <span class="fileupload-exists">Modificar</span> <input type="file" name="arquivo" id="arquivo" /></span>
                            <span class="fileupload-preview"></span>
                            <a href="#" class="close fileupload-exists" data-dismiss="fileupload" style="float: none">×</a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="configuracoes" style="padding-top: 10px;">
            <div class="col-md-6" style="border-right: 1px solid #e2e2e2;">

                <legend>
                    <h2>Monitor NFE</h2>
                </legend>

                <div class="well">

                    <div class="checkbox">
                        <label>
                            <input type="checkbox" name="retira_icms_preco" <?php echo set_checkbox('retira_icms_preco', 1, 1 == 1) ?> value="1"> Retira ICMS do Preço
                        </label>
                    </div>

                    <div class="checkbox">
                        <label>
                            <input type="checkbox" name="retira_pis_preco" <?php echo set_checkbox('retira_pis_preco', 1, 1 == 1) ?> value="1"> Retira PIS do Preço
                        </label>
                    </div>

                    <div class="checkbox">
                        <label>
                            <input type="checkbox" name="retira_cofins_preco" <?php echo set_checkbox('retira_cofins_preco', 1, 1 == 1) ?> value="1"> Retira COFINS do Preço
                        </label>
                    </div>

                    <div class="clearfix" style="margin-top: 10px">
                        <label for="tipo_tolerancia">Tipo da Tolerância</label>
                        <select class="form-control" name="tipo_tolerancia" id="tipo_tolerancia">
                            <option value="">Nenhum</option>
                            <option <?php echo set_select('tipo_tolerancia', 'valor') ?> value="valor">Valor</option>
                            <option <?php echo set_select('tipo_tolerancia', 'percentual') ?> value="percentual">Percentual</option>
                        </select>
                    </div>

                    <div class="clearfix" style="margin-top: 10px">
                        <label for="tolerancia">Tolerância</label>
                        <input type="text" placeholder="Valor Decimal (Ex.: 0.10)" class="form-control" name="tolerancia" id="tolerancia" value="<?php echo set_value('tolerancia') ?>">
                    </div>

                    <div class="clearfix" style="margin-top: 10px">
                        <label for="descricao_max_caracteres">Tamanho da descrição proposta resumida</label>
                        <input type="text" placeholder="Nº de caracteres (Ex.: 255)" class="form-control" name="descricao_max_caracteres" id="descricao_max_caracteres" value="<?php echo set_value('descricao_max_caracteres') ?>">
                    </div>

                </div>

                <legend>
                    <h2>Estabelecimento</h2>
                </legend>

                <div class="well">
                    <p>Informe o estabelecimento padrão:</p>
                    <input type="text" class="form-control" name="estabelecimento_default" maxlength="50" placeholder="Utilize letras e números com limite de 50 caracteres." value="<?php echo set_value('estabelecimento_default') ?>" />
                </div>

                <legend>
                    <h2>Usuário Fiscal</h2>
                </legend>
                <div class="well">
                    <p>Informe o usuário padrão:</p>
                    <select name="id_resp_fiscal" data-live-search="true" id="" data-size="5" class="selectpicker form-control">
                        <?php foreach ($usuarios as $usuario) : ?>
                            <option value="<?php echo $usuario->id_usuario; ?>">
                                <?php echo $usuario->nome; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>


                <?php $this->load->view('cadastros/empresa/concatenar-campos'); ?>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-12">
                        <legend>
                            <h2>Campos adicionais</h2>
                        </legend>

                        <div class="well">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="aplicacao" value="1" checked="checked"> Aplicação
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="caracteristica" value="1" checked="checked"> Característica
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="dispositivo_legal" value="1" checked="checked"> Dispositivos legais
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="descricao_proposta_completa" value="1" checked="checked"> Descrição proposta completa
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="descricao_mercado_local" value="1" checked="checked"> Descrição proposta resumida
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="descricao" value="1" checked="checked"> Descrição
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="evento" value="1" checked="checked"> Evento
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="funcao" value="1" checked="checked"> Função
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="inf_adicionais" value="1" checked="checked"> Informações Adicionais
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="marca" value="1" checked="checked"> Marca
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="material_constitutivo" value="1" checked="checked"> Material Constitutivo
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="memoria_classificacao" value="1" checked="checked"> Memória de Classificação
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="observacoes" value="1" checked="checked"> Observações
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="peso" value="1" checked="checked"> Peso
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="prioridade" value="1" checked="checked"> Prioridade
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="subsidio" value="1" checked="checked"> Subsidio
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="origem" value="1" checked="checked"> Origem (País)
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="maquina" value="1" checked="checked"> Máquina
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="pn_primario_secundario" value="1" checked="checked"> PN Primário/Secundário
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="controle_drawback" value="1"> Controle de Drawback
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="item_novo_ou_modificado" value="1" checked="checked"> Item novo ou modificado (S/N)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <legend>
                            <h2>Funções adicionais</h2>
                        </legend>

                        <div class="well">

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_atr" <?php echo set_checkbox('vinculacao_atr', 1, (in_array("vinculacao_atr", $funcoes_adicionais))) ?> value="1"> Atribuibutos
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_ii" <?php echo set_checkbox('vinculacao_ii', 1, (in_array("vinculacao_ii", $funcoes_adicionais))) ?> value="1"> Atribuição de EX II
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_ipi" <?php echo set_checkbox('vinculacao_ipi', 1, (in_array("vinculacao_ipi", $funcoes_adicionais))) ?> value="1"> Atribuição de EX IPI
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_nve" <?php echo set_checkbox('vinculacao_nve', 1, (in_array("vinculacao_nve", $funcoes_adicionais))) ?> value="1"> Atribuição de NVE
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_cest" <?php echo set_checkbox('vinculacao_cest', 1, (in_array("vinculacao_cest", $funcoes_adicionais))) ?> value="1"> Atribuição de CEST
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="ncm_dados_tecnicos" value="1"> Exibir NCM do item em Dados Técnicos
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="homologacao_fiscal" /> Homologação Fiscal
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="homologacao_engenharia" value="1"> Homologação Engenharia
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="recebe_email_pendencias" value="1"> Recebe Email Pendências
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="status_exportacao" <?php echo set_checkbox('status_exportacao', 1, (in_array("status_exportacao", $funcoes_adicionais))) ?>> Status Exportação Item (Homologação / Booking Eletrônico)
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="planilha-upload-input" value="planilha_upload" <?php echo set_checkbox('planilha_upload', 1, (in_array("planilha_upload", $funcoes_adicionais))) ?> value="1"> Exportar Planilha de Upload
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="exportar-input" value="exportar" <?php echo set_checkbox('exportar', 1, (in_array("exportar", $funcoes_adicionais))) ?> value="1"> Exportar Itens (Homologação / Booking Eletrônico)
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="classificacao-energetica-input" value="classificacao_energetica" <?php echo set_checkbox('classificacao_energetica', 1, (in_array("classificacao_energetica", $funcoes_adicionais))) ?> value="1"> Classificação Energética
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="exibir-id-gpt-input" value="exibir_id_gpt" <?php echo set_checkbox('exibir_id_gpt', 1, (in_array("exibir_id_gpt", $funcoes_adicionais))) ?> value="1"> Exibir ID do Grupo Tarifário na Exportação (Booking, Homologação e Planilha de Upload)
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="suframa-input" value="vinculacao_suframa" <?php echo set_checkbox('vinculacao_suframa', 1, (in_array("vinculacao_suframa", $funcoes_adicionais))) ?> value="1"> SUFRAMA
                                </label>
                            </div>

                            <div class="checkbox">
                                <label for="diana-input">
                                    <input type="checkbox" name="funcoes_adicionais[]" id="diana-input" value="has_diana" <?php echo set_checkbox('has_diana', 1, (in_array('has_diana', $funcoes_adicionais))) ?> value="1"> Utiliza DIANA
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="ncm_fornecedor-input" value="vinculacao_ncm_fornecedor" <?php echo set_checkbox('vinculacao_ncm_fornecedor', 1, (in_array("vinculacao_ncm_fornecedor", $funcoes_adicionais))) ?> value="1"> NCM Fornecedor
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="importar_itens_via_mestre-input" value="importar_itens" <?php echo set_checkbox('importar_itens', 1, (in_array("importar_itens", $funcoes_adicionais))) ?> value="1"> Importar Itens via Mestre
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="botao-preencher-desc-resumida-input" value="preencher_desc_resumida" <?php echo set_checkbox('preencher_desc_resumida', 1, (in_array('preencher_desc_resumida', $funcoes_adicionais))) ?> value="1"> Exibir Botão Preencher Descrição Proposta Resumida
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="formatar_texto" value="formatar_texto" <?php echo set_checkbox('formatar_texto', 1, (in_array('formatar_texto', $funcoes_adicionais))) ?> value="1"> Formatar Textos
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="lessin" value="lessin" <?php echo set_checkbox('lessin', 1, (in_array('lessin', $funcoes_adicionais))) ?> value="1"> Lessin
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="validar_pn" value="validar_pn" <?php echo set_checkbox('validar_pn', 1, (in_array('validar_pn', $funcoes_adicionais))) ?> value="1"> Validar PN de estabelecimentos diferentes para NCM divergentes
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="item_importado_default" value="item_importado_default" <?php echo set_checkbox('item_importado_default', 1, (in_array('item_importado_default', $funcoes_adicionais))) ?> value="1"> Definir item importado como padrão
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="diana_atributos" value="diana_atributos" <?php echo in_array("diana_atributos", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1">
                                    Utiliza Diana Atributos
                                </label>
                            </div>
                            
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="destinatatio_tec" value="destinatatio_tec" <?php echo set_checkbox('destinatatio_tec', 1, (in_array('destinatatio_tec', $funcoes_adicionais))) ?> value="1"> Destinatário TEC
                                </label>
                            </div>
                            
                            <div class="row mt-4 <?php echo in_array("destinatatio_tec", $funcoes_adicionais) ? '' : 'hide' ?>" id="div_destinatarios_tec">
                                <div class="col-md-12 mt-4">
                                    <div class="well">
                                        <p>Destinatários TEC:</p>
                                        <input name="destinatarios_tec" id="destinatarios_tec" type="text" class="form-control" placeholder="Informe os email's separadas por ; (ponto e virgula)" />
                                    </div>
                                </div>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="chk_destinatarios_revisao_pucomex" value="chk_destinatarios_revisao_pucomex" <?php echo set_checkbox('chk_destinatarios_revisao_pucomex', 1, (in_array('chk_destinatarios_revisao_pucomex', $funcoes_adicionais))) ?> value="1"> Destinatário TEC
                                </label>
                            </div>

                            <div class="row mt-4 <?php echo in_array("chk_destinatarios_revisao_pucomex", $funcoes_adicionais) ? '' : 'hide' ?>" id="div_destinatarios_revisao_pucomex">
                                <div class="col-md-12 mt-4">
                                    <div class="well">
                                        <p>Destinatários Revisão Pucomex</p>
                                        <input name="destinatarios_revisao_pucomex"  id="destinatarios_revisao_pucomex" type="text" class="form-control" placeholder="Informe os email's separadas por ; (ponto e virgula)" />
                                    </div>
                                </div>
                            </div>

                            <div class="checkbox">
                                <label for="homologacao_atributos_incompletos">
                                    <input
                                        type="checkbox"
                                        name="funcoes_adicionais[]"
                                        id="homologacao_atributos_incompletos"
                                        value="homologacao_atributos_incompletos"
                                        <?php echo in_array("homologacao_atributos_incompletos", $funcoes_adicionais) ? 'checked="checked"' : '' ?>>
                                    Permitir homologação de atributos incompletos
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="funcoes_adicionais[]"
                                        id="status_triagem_diana"
                                        value="status_triagem_diana" <?php echo in_array("status_triagem_diana", $funcoes_adicionais) ? 'checked="checked"' : '' ?>
                                        value="1"
                                    >
                                    Status Triagem Diana
                                </label>
                            </div>

                        </div>

                        <button type="button" id="suframa-button" class="btn btn-primary" style="display: none">Gerenciar Suframa</button>

                        <div class="modal fade" id="suframa-modal" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-xl">
                                <?php $this->load->view('cadastros/empresa/modal-suframa'); ?>
                            </div>
                        </div>

                        <?php $this->load->view('cadastros/empresa/modal-diana'); ?>
                    </div>

                </div>

                <div class="row">
                    <div class="col-md-12">
                        <legend>
                            <h2>CEST</h2>
                        </legend>
                        <div class="well">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="venda_porta_a_porta" value="1" <?php echo set_checkbox('venda_porta_a_porta', 1) ?> value="1"> Utiliza sistema de venda Porta a Porta
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="configuracoes_financeiras" style="padding-top: 10px;">
            <div class="col-md-12">
                <legend>
                    <h2>Controle de franquia</h2>
                </legend>
                <div class="well">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" id="habilitar_franquia_check" name="habilitar_uso_franquia" value="1" <?php echo $entry->habilitar_uso_franquia == 1 ? 'checked="checked"' : ''
                                                                                                                            ?>> Habilitar controle de franquia
                        </label>
                    </div>

                    <div id="controle_franquia_campos" style="margin-top: 15px; display: none;">

                        <div style="margin-left: 20px;">
                            <div class="form-group">
                                <label for="quantidade_franquia_mensal" style="font-size: 0.9em; font-weight: normal;">Quantidade da franquia controlada</label>
                                <input type="number"
                                    class="form-control"
                                    id="quantidade_franquia_mensal"
                                    name="quantidade_franquia_mensal"
                                    placeholder="Informe a quantidade"
                                    value="<?php echo set_value('quantidade_franquia_mensal', $entry->quantidade_franquia_mensal) ?>">
                            </div>
                        </div>
                        <hr>

                        <div style="padding-left: 20px; margin-top: 20px;">
                            <legend style="margin-bottom: 10px; font-size: 1.2em;">Valores de cobranças adicionais</legend>
                            <div class="checkbox">
                                <label><input type="checkbox" id="possui_cobranca_check" name="habilitar_cobrancas_adicionais" value="1" <?php echo $entry->habilitar_cobrancas_adicionais == 1 ? 'checked="checked"' : ''
                                                                                                                                            ?>> Possui cobrança adicional</label>
                            </div>
                            <div id="tabela_cobrancas_container" style="margin-top: 15px; display: none;">
                                <div style="margin-left: 20px;">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th style="width: 33%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Prioridade</th>
                                                <th style="width: 33%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Valor por item padrão</th>
                                                <th style="width: 34%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Valor por item químico</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <hr>

                        <div style="padding-left: 20px; margin-top: 20px;">
                            <legend style="margin-bottom: 10px; font-size: 1.2em;">Notificações de excedente</legend>
                            <div class="checkbox">
                                <label><input type="checkbox" id="habilitar_notificacoes_check" name="habilitar_notificacoes" value="1" <?php echo $entry->habilitar_notificacoes == 1 ? 'checked="checked"' : ''
                                                                                                                                        ?>> Habilitar notificações</label>
                            </div>
                            <div id="notificacoes_campos_container" style="display: none; margin-top: 15px;">
                                <div style="margin-left: 20px;">
                                    <div class="form-group">
                                        <label for="emails_notificados_input" style="font-size: 0.9em; font-weight: normal;">Email(s) notificado(s) <i class="glyphicon glyphicon-info-sign" title="Informe os emails separados por ponto e vírgula (;)"></i></label>
                                        <input type="text" class="form-control" id="emails_notificados_input" name="destinatarios_excedente" placeholder="Informe os email's separados por ; (ponto e virgula)" value="<?php echo set_value('destinatarios_excedente', $entry->destinatarios_excedente)
                                                                                                                                                                                                                        ?>">
                                    </div>
                                    <div class="form-group" style="margin-top: 20px;">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th style="width: 50%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Notificação</th>
                                                    <th style="width: 50%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Percentual de envio <i class="glyphicon glyphicon-info-sign" title="Percentual da franquia para disparar a notificação"></i></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                      
                      <td style="font-size: 0.9em; vertical-align: middle;">Primeira notificação</td>
                          <td>
                          <input type="text" class="form-control notificacao-input"
                          id="percentual_input"
                          name="percentual_primeira_notificacao"
                          value="">
                          
                      </td>
                      </tr>
                      <tr>
                          <td style="font-size: 0.9em; vertical-align: middle;">Segunda Notificação </td>
                          <td>
                          <input type="text"
                              class="form-control notificacao-input"
                              id="percentual_segunda_input"
                              name="percentual_segunda_notificacao"
                              value="">
                              </td>
                      </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>

                        <div style="padding-left: 20px; margin-top: 20px;">
                            <legend style="margin-bottom: 10px; font-size: 1.2em;">Bloqueio de cadastro de itens</legend>
                            <div class="checkbox">
                                <label><input type="checkbox" id="habilitar_bloqueio_check" name="habilitar_bloqueio" value="1" <?php echo $entry->habilitar_bloqueio == 1 ? 'checked="checked"' : ''
                                                                                                                                ?>> Habilitar bloqueio <i class="glyphicon glyphicon-info-sign" title="Habilita o bloqueio de cadastro ao atingir a franquia"></i></label>
                            </div>
                            <div id="bloqueio_campos_container" style="display: none; margin-top: 15px;">
                                <div style="margin-left: 20px;">
                                    <div class="form-group">
                                        <div style="display: flex; flex-wrap: wrap; gap: 20px; align-items: baseline;"> <label style="font-size: 0.9em; font-weight: normal; margin-bottom: 0;">
                                                <input type="radio" name="tipo_bloqueio" value="0" class="bloqueio-input" style="margin-right: 4px; vertical-align: middle;" <?php echo $entry->tipo_bloqueio ==  '0' ? 'checked' : ''
                                                                                                                                                                                ?>>Bloqueio após exceder a franquia
                                            </label>
                                            <label style="font-size: 0.9em; font-weight: normal; margin-bottom: 0;">
                                                <input type="radio" name="tipo_bloqueio" value="1" class="bloqueio-input" style="margin-right: 4px; vertical-align: middle;" <?php echo $entry->tipo_bloqueio ==  '1' ? 'checked' : ''
                                                                                                                                                                                ?>>Bloqueio imediato ao atingir a franquia
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-group" style="margin-top: 15px;">
    <label for="percentual_excedente_input" style="font-size: 0.9em; font-weight: normal;">
        Percentual de excedente 
        <i class="glyphicon glyphicon-info-sign" title="Percentual sobre a franquia antes de bloquear (se aplicável)"></i>
    </label>
    <input type="text"
        class="form-control bloqueio-input"
        id="percentual_excedente_input"
        name="percentual_excedente"
        value="">
</div>

                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="curva">
            <div class="col-md-12" style="padding-top: 30px;" id="container_curva">
                <?php
                $chr = ord('A');
                ?>
                <div class="row" style="padding-left: 20%;" id="row_<?php echo $chr; ?>" data-chr="<?php echo $chr; ?>">
                    <div class="form-group">
                        <label class="col-md-1 control-label">Classe: </label>
                        <div class="col-md-1">
                            <input type="text" name="classe[]" id="classe_<?php echo $chr; ?>" class="form-control text-center" value="<?php echo strtoupper(chr($chr)); ?>" readonly>
                        </div>
                        <label class="col-md-1 control-label">Percentual: </label>
                        <div class="col-md-1">
                            <input type="text" name="percentual[]" class="form-control" value="">
                        </div>
                        <label class="col-md-1 control-label">Status: </label>
                        <div class="col-md-3">
                            <select name="status[]" class="form-control">
                                <option value="" disabled selected>Selecione o status</option>
                                <option value="considera">Considera escopo</option>
                                <option value="nao_considera">Fora do escopo</option>
                            </select>
                        </div>
                        <a href="javascript:void(0);" class="btn btn-danger btn-sm delete-row" data-chr="<?php echo $chr; ?>"><i class="glyphicon glyphicon-trash"></i></a>
                    </div>
                </div>
            </div>
            <a id="add_row" class="btn btn-link">+ Adicionar classe</a>
        </div>
        <?php $this->load->view('cadastros/empresa/usuario-api'); ?>
    </div>
</div>
<div class="row" style="margin-bottom: 30px;">
    <hr />
    <div class="col-sm-5">
        <button type="submit" class="btn btn-primary" value="1" name="submit"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
        <a href="<?php echo site_url("cadastros/empresa") ?>" class="btn">Cancelar</a>
    </div>
</div>

</form>
<script>
    document.addEventListener('DOMContentLoaded', function() {

        // --- Referências aos Elementos ---
        const habilitarFranquiaCheck = document.getElementById('habilitar_franquia_check');
        const controleFranquiaCampos = document.getElementById('controle_franquia_campos'); // Container principal
        const qtdInput = document.getElementById('quantidade_franquia_mensal'); // Input direto da franquia

        const possuiCobrancaCheck = document.getElementById('possui_cobranca_check');
        const tabelaCobrancasContainer = document.getElementById('tabela_cobrancas_container');

        const habilitarNotificacoesCheck = document.getElementById('habilitar_notificacoes_check');
        const notificacoesCamposContainer = document.getElementById('notificacoes_campos_container');

        const habilitarBloqueioCheck = document.getElementById('habilitar_bloqueio_check');
        const bloqueioCamposContainer = document.getElementById('bloqueio_campos_container');

        // --- Funções de Toggle ---

        function toggleCobrancaContainerVisibility() {
            if (possuiCobrancaCheck && tabelaCobrancasContainer && habilitarFranquiaCheck && habilitarFranquiaCheck.checked) {
                const isChecked = possuiCobrancaCheck.checked;
                tabelaCobrancasContainer.style.display = isChecked ? 'block' : 'none';
                const inputs = tabelaCobrancasContainer.querySelectorAll('.cobranca-input');
                inputs.forEach(input => input.disabled = !isChecked);
            } else if (tabelaCobrancasContainer) {
                tabelaCobrancasContainer.style.display = 'none';
            }
        }

        function toggleNotificacoesVisibility() {
            if (habilitarNotificacoesCheck && notificacoesCamposContainer && habilitarFranquiaCheck && habilitarFranquiaCheck.checked) {
                const isChecked = habilitarNotificacoesCheck.checked;
                notificacoesCamposContainer.style.display = isChecked ? 'block' : 'none';
                const inputs = notificacoesCamposContainer.querySelectorAll('input.form-control');
                inputs.forEach(input => {
                    if (!input.hasAttribute('readonly')) {
                        input.disabled = !isChecked;
                    }
                });
            } else if (notificacoesCamposContainer) {
                notificacoesCamposContainer.style.display = 'none';
            }
        }

        function toggleBloqueioVisibility() {
            if (habilitarBloqueioCheck && bloqueioCamposContainer && habilitarFranquiaCheck && habilitarFranquiaCheck.checked) {
                const isChecked = habilitarBloqueioCheck.checked;
                bloqueioCamposContainer.style.display = isChecked ? 'block' : 'none';
                const inputs = bloqueioCamposContainer.querySelectorAll('.bloqueio-input');
                inputs.forEach(input => input.disabled = !isChecked);
            } else if (bloqueioCamposContainer) {
                bloqueioCamposContainer.style.display = 'none';
            }
        }

        function toggleFranquiaCamposVisibility() {
            if (habilitarFranquiaCheck && controleFranquiaCampos) {
                const isChecked = habilitarFranquiaCheck.checked;
                controleFranquiaCampos.style.display = isChecked ? 'block' : 'none';
                if (qtdInput) qtdInput.disabled = !isChecked;
                toggleCobrancaContainerVisibility();
                toggleNotificacoesVisibility();
                toggleBloqueioVisibility();
            }
        }

        // --- Adiciona Listeners ---
        if (habilitarFranquiaCheck) {
            habilitarFranquiaCheck.addEventListener('change', toggleFranquiaCamposVisibility);
        }
        if (possuiCobrancaCheck) {
            possuiCobrancaCheck.addEventListener('change', toggleCobrancaContainerVisibility);
        }
        if (habilitarNotificacoesCheck) {
            habilitarNotificacoesCheck.addEventListener('change', toggleNotificacoesVisibility);
        }
        if (habilitarBloqueioCheck) {
            habilitarBloqueioCheck.addEventListener('change', toggleBloqueioVisibility);
        }

        toggleFranquiaCamposVisibility();

        // Adicional: Tooltips (código comentado como antes)
        /*
        if (typeof $ === 'function' && typeof $.fn.tooltip === 'function') {
            $('.glyphicon-info-sign').tooltip({ placement: 'right', trigger: 'hover' });
        }
        */
    });

    $('form').on('submit', function(e) {
        const primeira = parseInt($('[name="percentual_primeira_notificacao"]').val());
        const segunda = parseInt($('[name="percentual_segunda_notificacao"]').val());
        const excedente = parseInt($('[name="percentual_excedente_input"]').val());
        var checkboxMarcadoNotificacoes = $('#habilitar_notificacoes_check').is(':checked');
        if (checkboxMarcadoNotificacoes && (excedente < 1 || excedente > 100)) {
            swal("Atenção", "A o percentual excedente deve ser entre 1 e 100.", "warning");
            $('[name="percentual_excedente_input"]').focus();
            e.preventDefault();
            return;
        }

        if (checkboxMarcadoNotificacoes && (primeira < 1 || primeira > 99)) {
            swal("Atenção", "A Primeira Notificação deve ser entre 1 e 99.", "warning");
            $('[name="percentual_primeira_notificacao"]').focus();
            e.preventDefault();
            return;
        }

        if (checkboxMarcadoNotificacoes && (segunda <= primeira || segunda > 100)) {
            swal("Atenção", "A Segunda Notificação deve ser maior que a Primeira.", "warning");
            $('[name="percentual_segunda_notificacao"]').focus();
            e.preventDefault();
            return;
        }
    });

    $('body').on('keypress', '.cobranca-input', function(event) {
        var $this = $(this);
        var charCode = (event.which) ? event.which : event.keyCode;
        var value = $this.val();
        var cursorPosition = this.selectionStart;

        // Permite: números (0-9)
        if (charCode >= 48 && charCode <= 57) {
            var commaIndex = value.indexOf(',');
            if (commaIndex !== -1 && cursorPosition > commaIndex && value.substring(commaIndex + 1).length >= 2) {
                if (this.selectionStart === this.selectionEnd) { // Nenhuma seleção
                    event.preventDefault();
                }
            }
            return true;
        }

        // Permite: uma única VÍRGULA decimal
        if (charCode === 44) { // Vírgula (,)
            if (value.indexOf(',') === -1) { // Só permite se não houver outra vírgula
                return true;
            } else {
                event.preventDefault();
                return false;
            }
        }

        // Permite: teclas de controle como backspace, delete, tab, escape, enter, setas
        if (charCode === 8 || charCode === 46 || charCode === 9 || charCode === 27 || charCode === 13 ||
            (charCode >= 35 && charCode <= 40)) {
            return true;
        }
        // Permite: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Command+A (para Mac)
        if ((event.ctrlKey || event.metaKey) && (charCode === 97 || charCode === 99 || charCode === 118 || charCode === 120 || charCode === 65 || charCode === 67 || charCode === 86 || charCode === 88)) {
            return true;
        }

        // Bloqueia todas as outras teclas
        event.preventDefault();
        return false;
    });

    $('body').on('blur', '.cobranca-input', function() {
        var $this = $(this);
        var value = $this.val();

        if (value === "" || value === null) {
            $this.val(""); // Garante que seja uma string vazia
            return;
        }

        // 1. Substitui a vírgula por ponto para o processamento interno (parseFloat)
        var valueForParsing = value.replace(',', '.');

        // 2. Limpeza adicional: remove caracteres não numéricos exceto o primeiro ponto
        // (após a conversão da vírgula, só deve haver pontos aqui)
        var cleanedValue = valueForParsing.replace(/[^0-9.-]+/g, "");
        var parts = cleanedValue.split('.');
        if (parts.length > 2) { // Remove pontos extras se houver
            cleanedValue = parts[0] + '.' + parts.slice(1).join('');
        }

        var num = parseFloat(cleanedValue);

        if (!isNaN(num)) {
            // 3. Formata para duas casas decimais (toFixed sempre usa ponto)
            var formattedWithDot = num.toFixed(2);
            // 4. Substitui o ponto por vírgula para exibição
            var formattedWithComma = formattedWithDot.replace('.', ',');
            $this.val(formattedWithComma);
        } else {
            // Se não for um número válido após a limpeza (ex: ",", "abc")
            $this.val(""); // Limpa o campo
        }
    });
    $(document).ready(function() {
        $('#quantidade_franquia_mensal').on('keydown', function(e) {
            // Bloqueia a tecla "-" (código 189 e 109 para numpad)
            if (e.key === '-' || e.keyCode === 189 || e.keyCode === 109) {
                e.preventDefault();
            }
        });

        $('#quantidade_franquia_mensal').on('input', function() {
            // Remove qualquer sinal de menos que tenha sido colado
            let valor = $(this).val();
            valor = valor.replace(/-/g, '');
            $(this).val(valor);
        });
    });

    $(document).ready(function() {
                $('form').on('submit', function(e) {
                    var checkboxMarcado = $('#habilitar_notificacoes_check').is(':checked');
                    var campoEmailVazio = $('#emails_notificados_input').val() === '';
 
                    if (checkboxMarcado && campoEmailVazio) {
                        swal("Atenção", "Você deve preencher os e-mails que receberão a notificação.", "warning");
                        e.preventDefault();  
                    }
                });

            });
</script>
<script type="text/javascript">
    $(document).ready(function() {
        $('#destinatatio_tec').change(function() {
            if ($(this).is(':checked')) {

                $('#div_destinatarios_tec').removeClass('hide');
            } else {
                $('#div_destinatarios_tec').addClass('hide');
            }
        });
    });

    $(document).ready(function() {
        $('#chk_destinatarios_revisao_pucomex').change(function() {
            if ($(this).is(':checked')) {

                $('#div_destinatarios_revisao_pucomex').removeClass('hide');
            } else {
                $('#div_destinatarios_revisao_pucomex').addClass('hide');
            }
        });
    });

    
    $('#add_row').on('click', function() {

        var last_row_added = $('#container_curva').children().last();
        var ascii = $(last_row_added).attr('data-chr');

        //se for z, impedir que sejam adicionados novos rows;
        if (parseInt(ascii) == 122) {
            return true;
        }

        var new_ascii = parseInt(ascii) + 1;
        var new_row = $(last_row_added).clone(true).appendTo('#container_curva');

        $(last_row_added).find('.delete-row').remove();

        $(new_row).attr('data-chr', new_ascii);
        $(new_row).attr('id', 'row_' + new_ascii);

        var new_chr = String.fromCharCode(new_ascii);

        $(new_row).find('.delete-row').attr('data-chr', new_ascii);
        $(new_row).find('#classe_' + ascii).attr('id', 'classe_' + new_ascii).val(new_chr);
    });

    $('.delete-row').on('click', function() {

        if ($('#container_curva').children().length == 1) {
            return true;
        }

        chr = $(this).attr('data-chr');

        old_chr = parseInt(chr - 1);
        var new_remove = $(this).clone(true).appendTo('#row_' + old_chr + ' .form-group');
        $(new_remove).attr('data-chr', old_chr);

        $('#row_' + chr).remove();
    });
</script>

<script>
document.addEventListener("DOMContentLoaded", function () {
    const percentualInputs = [
        document.getElementById("percentual_input"),
        document.getElementById("percentual_segunda_input"),
        document.getElementById("percentual_excedente_input")
    ];
    const emailInput = document.getElementById('emails_notificados_input');

    emailInput.addEventListener('blur', function () {
        const value = emailInput.value.trim();
        
        if (value && !value.includes('@')) {
      
            swal('Atenção!', 'Deve ser um email válido', 'warning');
            emailInput.value = '';
        }
    });

    percentualInputs.forEach(input => {
        // Ao digitar, adiciona % automaticamente
        input.addEventListener("input", function () {
            let val = this.value.replace(/\D/g, ''); // remove não números
            let num = parseInt(val);

            if (!isNaN(num) && num >= 1 && num <= 100) {
                this.value = num + '%';
            } else if (val === '') {
                this.value = '';
            } else {
                this.value = '100%'; // máximo permitido
            }
        });

        // Ao focar, remove o %
        input.addEventListener("focus", function () {
            this.value = this.value.replace('%', '');
        });

        // Ao sair do campo, garante que o valor fique com %
        input.addEventListener("blur", function () {
            let val = this.value.replace(/\D/g, '');
            let num = parseInt(val);

            if (!isNaN(num) && num >= 1 && num <= 100) {
                this.value = num + '%';
            } else {
                this.value = '';
            }
        });
    });



    const radioButtons = document.querySelectorAll('input[name="tipo_bloqueio"]');
    const percentualInputContainer = document.getElementById('percentual_excedente_input').closest('.form-group');
    const percentualInput = document.getElementById('percentual_excedente_input');

    function togglePercentualInput(value) {
        if (value === '1') {
            percentualInput.value = '';
            percentualInputContainer.style.display = 'none';
        } else {
            percentualInputContainer.style.display = 'block';
        }
    }

    radioButtons.forEach(radio => {
        radio.addEventListener('change', function () {
            togglePercentualInput(this.value);
        });

        if (radio.checked) {
            togglePercentualInput(radio.value);
        }
    });
});
</script>